<template>
  <div style="height: 100%">
    <gever-dialog
      :title="dialogTitle"
      :visible="visible"
      width="1350px"
      top="5vh"
      :append-to-body="appendToBody"
      custom-class="voucher"
      :close-on-click-modal="false"
      :show-close="false"
      destroy-on-close
      @close="handleClose"
    >
      <div v-loading="loading" class="box-container">
        <div v-if="showTopBtns" class="top-btns">
          <el-button type="primary" round plain @click="handleSaveSummary">
            保存摘要
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-s-tools"
            round
            plain
            @click="handleMaintainSummary"
          >
            维护摘要
          </el-button>
          <el-button
            v-if="loadTypeLocal === 'add' || loadTypeLocal === 'edit'"
            type="primary"
            round
            plain
            @click="handleImportVoucher"
          >
            凭证导入
          </el-button>
          <el-button
            v-if="loadTypeLocal === 'add' || loadTypeLocal === 'edit'"
            type="primary"
            round
            plain
            @click="handleAddCommonVoucher"
          >
            凭证存档
          </el-button>
          <el-button type="primary" round plain @click="handleViewHelp">
            使用说明
          </el-button>
          <div
            v-if="
              voucher.page &&
                no_pager === '0' &&
                (loadTypeLocal === 'view' || loadTypeLocal === 'edit')
            "
            class="pager"
          >
            <i
              :class="voucher.page.first ? 'active' : ''"
              class="el-customIcon-zuiqian normal"
              @click="goPage(voucher.page.first)"
            />
            <i
              :class="voucher.page.prev ? 'active' : ''"
              class="el-customIcon-left normal"
              @click="goPage(voucher.page.prev)"
            />
            <i
              :class="voucher.page.next ? 'active' : ''"
              class="el-customIcon-right normal"
              @click="goPage(voucher.page.next)"
            />
            <i
              :class="voucher.page.end ? 'active' : ''"
              class="el-customIcon-zuihou normal"
              @click="goPage(voucher.page.end)"
            />
          </div>
        </div>
        <div class="header-info">
          <div class="left-info">
            <span class="name">
              {{ $t('账套：') }}
              <span>{{ booksName }}</span>
            </span>
            <span class="balance">
              {{ $t('当前科目余额（已记账）：') }}
              <span>{{ currentSubjectBalance }}</span>
            </span>
            <span class="balance">
              {{ $t('当前科目余额（已制单）：') }}
              <span>{{ allEndBal }}</span>
            </span>
            <span class="name" v-text="$t('凭证字：') + voucher.voucherName" />
          </div>
          <div class="center-info">
            <img
              :src="require('@/assets/images/voucher/certificatelogo.png')"
            />
            <span class="number">
              {{ $t('凭证号:') + voucher.voucherNumber }}
            </span>
          </div>
          <div class="right-info">
            <template
              v-if="
                loadType === 'add' &&
                  originId &&
                  voucher.deferMonthended === '1'
              "
            >
              <span>期间:</span>
              <!-- <el-date-picker v-model="voucher.businessDate" :disabled="loadTypeLocal === 'view'" :picker-options="pickerOptions" value-format="yyyy-MM-dd" type="date" /> -->
              <gever-select
                ref="_periodRef"
                v-model="voucher.period"
                number-key
                class="w100"
                label-prop="name"
                :clearable="false"
                :options="booksPeriod"
                @change="changePeriod"
              />
            </template>
            <span>日期:</span>
            <el-date-picker
              v-model="voucher.businessDate"
              :disabled="loadTypeLocal === 'view'"
              value-format="yyyy-MM-dd"
              type="date"
            />
            <!-- :picker-options="pickerOptions" -->
            <span>附单据:</span>
            <el-input-number
              v-model="voucher.receiptSheets"
              :controls="false"
              :precision="0"
              :min="0"
              :max="9999"
              :title="voucher.receiptSheets"
              :disabled="loadTypeLocal === 'view'"
              @keyup.enter.native="handleEnter"
            />
            <span>张</span>
          </div>
        </div>

        <div class="table">
          <ul
            class="table-header"
            :class="{ 'table-retract': voucher.entryList.length > 5 }"
          >
            <li class="cell">序号</li>
            <li class="cell">
              <span>摘要</span>
              <span class="info">(可录入300个字符)</span>
            </li>
            <li class="cell" style="width: 27%">会计科目</li>
            <li class="cell">借方金额</li>
            <li class="cell">贷方金额</li>
          </ul>
          <!-- <vxe-list
            id="table-body"
            :auto-resize="true"
            class="table-body"
            height="300"
            :data="voucher.entryList"
            :scroll-y="{ gt: 10 }"
          >

          </vxe-list> -->
          <div id="table-body" class="table-body">
            <!-- :style="{ marginRight: voucher.entryList.length > 5 ? '-6px' : '' }" -->
            <!-- <template> -->
            <ul
              v-for="(item, key) in voucher.entryList"
              :key="key"
              class="table-body-row"
              @click="handleEntryClick(item)"
            >
              <i
                v-if="loadTypeLocal !== 'view'"
                class="row-operation el-icon-top"
                @click="handleTop(key)"
              />
              <i
                v-if="loadTypeLocal !== 'view'"
                class="row-operation el-icon-circle-plus-outline"
                @click="handleAdd(key)"
              />
              <i
                v-if="loadTypeLocal !== 'view'"
                class="row-operation el-icon-circle-close"
                @click="handleRemove(key, item)"
              />
              <i
                v-if="loadTypeLocal !== 'view'"
                class="row-operation el-icon-bottom"
                @click="handleBottom(key)"
              />
              <!-- <li class="cell">{{ key + 1 ? key + 1 : '' }}</li> -->
              <li class="cell">{{ key + 1 }}</li>
              <li class="cell">
                <el-popover
                  :ref="'_summaryRef' + (key * 4 + 0)"
                  :trigger="summaryList.length ? 'focus' : 'manual'"
                  popper-class="voucher-popper"
                  :append-to-body="false"
                  :visible-arrow="false"
                  placement="bottom"
                  :disabled="loadTypeLocal === 'view'"
                >
                  <ul
                    v-if="summaryList.length"
                    style="max-height: 300px; overflow-y: auto"
                  >
                    <li
                      v-for="(summary, index) in summaryList"
                      :key="index"
                      class="li-item"
                      :title="summary.name"
                      style="
                        overflow-x: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                      @click="handleSummaryClick(item, summary)"
                    >
                      {{ summary.name }}
                    </li>
                  </ul>
                  <gever-input
                    slot="reference"
                    :ref="'_inputItem' + (key * 4 + 0)"
                    v-model="item.summary"
                    class="cell-data"
                    maxlength="300"
                    :show-word-limit="false"
                    type="textarea"
                    :readonly="loadTypeLocal === 'view'"
                    @focus="handleSummaryFocus($event, item, key)"
                    @keydown.native="
                      handleShortcutKeyEvent($event, item, key * 4 + 0)
                    "
                  />
                </el-popover>
              </li>

              <li class="cell subject-cell" style="width: 27%">
                <div
                  v-if="!assistComplete(item) && item.assistVisible"
                  id="assist-mask"
                />
                <!-- 辅助核算popover -->
                <el-popover
                  v-model="item.assistVisible"
                  trigger="manual"
                  :visible-arrow="false"
                  :append-to-body="false"
                  popper-class="voucher-popper assist-popper"
                  placement="bottom"
                >
                  <div slot="reference" class="assist-popper-reference" />
                  <div
                    v-if="item.assistVisible"
                    class="assist-form"
                    :tabindex="key"
                    style="height: 200px; overflow: auto"
                  >
                    <el-form inline label-width="100px" @submit.native.prevent>
                      <el-form-item
                        v-for="(assist, assistIndex) in assistList"
                        :key="assist.id"
                        :label="assist.name"
                        style="width: 100%"
                      >
                        <vxe-pulldown
                          :ref="'assistPulldown' + key + assistIndex"
                          class="voucher-popper assist-form-popper"
                          :transfer="true"
                          destroy-on-close
                          @hide-panel="
                            hidePanel(
                              item,
                              key,
                              $event,
                              assistIndex,
                              'noSearch'
                            )
                          "
                        >
                          <template #default>
                            <el-input
                              :value="getValue(item, assistIndex)"
                              placeholder="请输入关键字搜索"
                              :clearable="false"
                              @focus="focusAssistArr(key, assistIndex, item)"
                              @input="
                                handleAssistChange(
                                  item,
                                  key,
                                  $event,
                                  assistIndex,
                                  ''
                                )
                              "
                              @blur="
                                handleAssistBlur(item, key, $event, assistIndex)
                              "
                            />
                          </template>
                          <template #dropdown>
                            <div class="voucher-popper">
                              <vxe-list
                                v-if="vxeFzShow"
                                height="210"
                                :data="assist.assistPrjList"
                              >
                                <template #default="{ items }">
                                  <div class="subject-list">
                                    <div
                                      v-for="assistItem in items"
                                      :key="assistItem.id"
                                      class="li-item vxe-li"
                                      :title="
                                        assistItem.code.toString() +
                                          '-' +
                                          assistItem.name.toString()
                                      "
                                      @click="
                                        handleselectAssistItem(
                                          assistItem,
                                          item,
                                          key,
                                          assistIndex
                                        )
                                      "
                                    >
                                      <span>
                                        {{
                                          assistItem.code.toString() +
                                            '-' +
                                            assistItem.name.toString()
                                        }}
                                      </span>
                                    </div>
                                  </div>
                                </template>
                              </vxe-list>
                              <el-button
                                type="text"
                                icon="el-icon-s-tools"
                                @click="
                                  handleSubjectVisiblInner(key, assistIndex)
                                "
                              >
                                维护会计科目
                              </el-button>
                            </div>
                          </template>
                        </vxe-pulldown>
                        <el-button
                          type="text"
                          @click="handleAccounting(assist, item, key)"
                        >
                          新增项目
                        </el-button>
                      </el-form-item>
                    </el-form>
                    <el-button @click="handleRechooseSubject(item, key)">
                      {{ $t('重选科目') }}
                    </el-button>
                  </div>
                </el-popover>

                <!-- 科目选择popover -->
                <vxe-pulldown ref="xDown2" :transfer="true" destroy-on-close>
                  <template #default>
                    <gever-input
                      v-if="showBal(item)"
                      slot="reference"
                      :ref="'_inputItem' + (key * 4 + 1)"
                      :value="
                        item.accountingItemInputShowName ||
                          ((
                            item.assistProjectId
                              ? item.accountingItemShowName + item.assistName
                              : item.accountingItemShowName
                          )
                            ? (item.assistProjectId
                              ? item.accountingItemShowName + item.assistName
                              : item.accountingItemShowName
                            ).replace('undefined', '')
                            : '')
                      "
                      :placeholder="item.accountingItemHint"
                      class="cell-data"
                      type="textarea"
                      :rows="2"
                      :readonly="
                        loadTypeLocal === 'view' || subjectEditLimit(item)
                      "
                      @input="handleAccountingItemInput($event, item)"
                      @focus="handleAccountingItemFocus(item)"
                      @blur="handleAccountingItemBlur(item, key)"
                      @click.stop.native="handleClick(item, key)"
                      @keydown.native="
                        handleShortcutKeyEvent($event, item, key * 4 + 1)
                      "
                    />
                    <gever-input
                      v-else
                      slot="reference"
                      :ref="'_inputItem' + (key * 4 + 1)"
                      :value="
                        item.accountingItemInputShowName ||
                          ((
                            item.assistProjectId
                              ? item.accountingItemShowName + item.assistName
                              : item.accountingItemShowName
                          )
                            ? (item.assistProjectId
                              ? item.accountingItemShowName + item.assistName
                              : item.accountingItemShowName
                            ).replace('undefined', '')
                            : '')
                      "
                      :placeholder="item.accountingItemHint"
                      class="cell-data full-cell"
                      type="textarea"
                      :rows="2"
                      :readonly="
                        loadTypeLocal === 'view' || subjectEditLimit(item)
                      "
                      @input="handleAccountingItemInput($event, item)"
                      @focus="handleAccountingItemFocus(item, key)"
                      @click.stop.native="handleClick(item, key)"
                      @keydown.native="
                        handleShortcutKeyEvent($event, item, key * 4 + 1)
                      "
                      @blur="handleAccountingItemBlur(item, key)"
                    />
                  </template>
                  <template #dropdown>
                    <div class="voucher-popper">
                      <vxe-list
                        v-if="vxeShow"
                        :ref="'_vxeListRef' + key"
                        class="my-dropdown2"
                        height="300"
                        :data="currentSubjectList"
                      >
                        <template #default="{ items }">
                          <div class="subject-list">
                            <div
                              v-for="subject in items"
                              :key="subject.id"
                              style="height: 30px"
                              class="li-item vxe-li"
                              :title="subject.text"
                              :class="
                                focusSubjectId === subject.id ? 'isFocus' : ''
                              "
                              @click="
                                handleSelectSubject(subject, item, key, true)
                              "
                            >
                              <span v-if="subject.leaf" class="red">
                                {{ subject.text }}
                              </span>
                              <span
                                v-else-if="subject.assistFlag"
                                style="color: rgb(0, 0, 255)"
                              >
                                {{ subject.text }}
                              </span>
                              <span v-else>{{ subject.text }}</span>
                            </div>
                          </div>
                        </template>
                      </vxe-list>
                      <el-button
                        type="text"
                        icon="el-icon-s-tools"
                        @click="handleSubjectVisible(key)"
                      >
                        维护会计科目22
                      </el-button>
                    </div>
                  </template>
                </vxe-pulldown>
                <el-button
                  v-if="curKey == key"
                  size="medium"
                  type="text"
                  @click="handleAddSubject(item)"
                >
                  科目
                </el-button>
                <div v-if="showBal(item)" class="row-balance">
                  {{ $t('余额：') + item.subjectBalanceInRow }}
                </div>
              </li>

              <li class="cell">
                <div
                  v-show="!item.debitShow"
                  :ref="'_div' + (key * 4 + 2)"
                  class="cell-mask"
                  :class="item.debit < 0 ? 'red' : ''"
                  @click="handleShowDebitInput(key)"
                >
                  {{ amountToString(item.debit) }}
                </div>
                <el-popover
                  v-show="item.debitShow"
                  trigger="focus"
                  placement="top"
                  popper-class="voucher-popper amount-popper"
                  :append-to-body="false"
                  :visible-arrow="false"
                >
                  <div class="debit">
                    {{ amountToDottedString(item.debit) }}
                  </div>
                  <el-input
                    slot="reference"
                    :ref="'_inputItem' + (key * 4 + 2)"
                    :value="item.debit"
                    class="cell-data"
                    oninput="value=((value.replace(/[^0-9-.]/g,'')).replace(/([0-9]+\.[0-9]{2})[0-9]*/,'$1').replace(/(?<=\d)-/g,''))"
                    @input="handleDebitInput($event, item)"
                    @focus="handleDebitFocus($event, item)"
                    @blur="handleBlur($event, item, 'debitShow')"
                    @keydown.native="
                      handleShortcutKeyEvent($event, item, key * 4 + 2)
                    "
                  />
                </el-popover>
              </li>
              <li class="cell pop-cell">
                <div
                  v-show="!item.creditShow"
                  :ref="'_div' + (key * 4 + 3)"
                  class="cell-mask"
                  :class="item.credit < 0 ? 'red' : ''"
                  @click="handleShowCreditInput(key)"
                >
                  {{ amountToString(item.credit) }}
                </div>
                <el-popover
                  v-show="item.creditShow"
                  trigger="focus"
                  placement="top"
                  popper-class="voucher-popper amount-popper"
                  :append-to-body="false"
                  :visible-arrow="false"
                >
                  <div class="debit">
                    {{ amountToDottedString(item.credit) }}
                  </div>
                  <el-input
                    slot="reference"
                    :ref="'_inputItem' + (key * 4 + 3)"
                    :value="item.credit"
                    class="cell-data"
                    oninput="value=((value.replace(/[^0-9-.]/g,'')).replace(/([0-9]+\.[0-9]{2})[0-9]*/,'$1').replace(/(?<=\d)-/g,''))"
                    @input="handleCreditInput($event, item)"
                    @focus="handleCreditFocus($event, item)"
                    @blur="handleBlur($event, item, 'creditShow')"
                    @keydown.native="
                      handleShortcutKeyEvent($event, item, key * 4 + 3)
                    "
                  />
                </el-popover>
              </li>
            </ul>
            <!-- </template> -->
          </div>
          <ul
            class="table-footer"
            :class="{ 'table-retract': voucher.entryList.length > 5 }"
          >
            <li class="cell" style="width: 28%" />
            <li class="cell" style="width: 27%">合计:</li>
            <li class="cell">
              <div class="data" :class="debitTotalAmount < 0 ? 'red' : ''">
                {{ amountToString(debitTotalAmount) }}
              </div>
            </li>
            <li class="cell" :class="creditTotalAmount < 0 ? 'red' : ''">
              <div class="data">
                {{ amountToString(creditTotalAmount) }}
              </div>
            </li>
          </ul>
        </div>

        <div class="footer-info">
          <el-form inline style="float: right" :disabled="true">
            <el-form-item label="审核:">
              <gever-input v-model="voucher.checkerName" />
            </el-form-item>
            <el-form-item label="记账:">
              <gever-input v-model="voucher.bookkeeperName" />
            </el-form-item>
            <el-form-item label="制单人:">
              <gever-input v-model="voucher.creatorName" />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <span v-if="loadTypeLocal !== 'view'" slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDir(false)">{{ $t('关 闭') }}</el-button>
        <el-button
          type="primary"
          @click="handleSaveVoucher(false, 0, false, 1)"
        >
          {{ $t('暂存') }}
        </el-button>
        <el-button
          type="primary"
          @click="handleSaveVoucher(false, 1, false, 0)"
        >
          {{ $t('保存并关闭') }}
        </el-button>
        <el-button type="primary" @click="handleSaveVoucher(true, 0, false, 0)">
          {{ $t('保 存') }}
        </el-button>
        <el-button
          v-if="loadTypeLocal === 'add'"
          type="primary"
          @click="handleSaveVoucher(true, 0, true, 0)"
        >
          {{ $t('保存并新增') }}
        </el-button>
      </span>
      <span v-else style="float: right">
        <el-button @click="handleClose(false)">{{ $t('关 闭') }}</el-button>
      </span>
    </gever-dialog>

    <!-- 常用摘要维护 -->
    <el-dialog
      :title="$t('常用摘要')"
      :visible.sync="summaryVisible"
      append-to-body
      top="12vh"
      width="800px"
      @close="handleSummaryDialogClose"
    >
      <el-form inline>
        <el-form-item :label="$t('常用摘要:')">
          <gever-input v-model="summaryQueryParams.summary" />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            round
            plain
            icon="el-icon-search"
            @click="loadSummaryList"
          >
            {{ $t('搜索') }}
          </el-button>
        </el-form-item>
        <div style="float: right">
          <el-form-item>
            <el-button
              type="primary"
              round
              icon="el-icon-plus"
              @click="handleAddSummary"
            >
              {{ $t('新增') }}
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              round
              plain
              icon="el-icon-delete"
              @click="handleBatchRemoveSummary"
            >
              {{ $t('删除') }}
            </el-button>
          </el-form-item>
        </div>
      </el-form>
      <gever-table
        ref="_geverTableRef"
        v-loading="summaryLoading"
        class="summary-mantiance-table"
        :columns="summaryTableColumns"
        :data="summaryTable.dataList"
        :total="summaryTable.total"
        :pagi="summaryQueryParams"
        :height="300"
        @p-current-change="loadSummaryList"
        @size-change="loadSummaryList"
      >
        <template #operation="{ row }">
          <el-button type="text" @click="handleViewSummary(row)">
            {{ $t('查看') }}
          </el-button>
          <el-button type="text" @click="handleEditSummary(row)">
            {{ $t('编辑') }}
          </el-button>
          <el-button type="text" @click="handleRemoveSummary(row, false)">
            {{ $t('删除') }}
          </el-button>
        </template>
      </gever-table>
    </el-dialog>

    <!-- 常用摘要信息 -->
    <el-dialog
      :title="$t(summaryInfoTitle)"
      :visible.sync="summaryInfoVisible"
      append-to-body
      width="600px"
    >
      <el-form
        ref="_summaryRef"
        v-loading="summaryInfoLoading"
        class="gever-form"
        inline
        :model="summaryInfo"
        :rules="summaryInfoRules"
        :disabled="isSummaryInfoView"
      >
        <div class="form-sg">
          <el-form-item :label="$t('编号')" prop="code">
            <gever-input v-model="summaryInfo.code" />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('摘要')" prop="summary">
            <gever-input
              v-model="summaryInfo.summary"
              type="textarea"
              :rows="5"
              maxlength="100"
              resize="false"
            />
          </el-form-item>
        </div>
      </el-form>

      <span v-if="!isSummaryInfoView" slot="footer" class="dialog-footer">
        <el-button @click="summaryInfoVisible = false">
          {{ $t('关 闭') }}
        </el-button>
        <el-button type="primary" @click="handleSaveSummaryInfo">
          {{ $t('保 存') }}
        </el-button>
      </span>
    </el-dialog>

    <!-- 会计科目维护 -->
    <public-drawer
      :visible.sync="subjectVisible"
      :title="$t('账套-会计科目维护')"
      :size="90"
      @close="getModelItemList"
    >
      <subjectComponent v-if="subjectVisible" />
    </public-drawer>

    <!--<el-dialog
      :title="$t('账套-会计科目维护')"
      :visible.sync="subjectVisible"
      width="1200px"
      @closed="getModelItemList"
    >
      <subject style="height: 550px" />
      <span v-if="isSummaryInfoView" slot="footer" class="dialog-footer">
        <el-button round @click="subjectVisible = false">
          {{ $t('关 闭') }}
        </el-button>
      </span>
    </el-dialog>-->

    <!-- 凭证导入 -->
    <el-dialog
      :title="$t('常用凭证')"
      :visible.sync="importVisible"
      append-to-body
    >
      <el-form inline>
        <el-form-item :label="$t('常用凭证名称:')">
          <gever-input v-model="commonVoucherQueryParams.voucherName" />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            plain
            round
            icon="el-icon-search"
            @click="loadCommonVoucherList"
          >
            {{ $t('搜索') }}
          </el-button>
        </el-form-item>
        <div style="float: right">
          <!--<el-form-item>
            <el-button
              type="primary"
              plain
              round
              icon="el-icon-edit-outline"
              @click="handleBatchEditCommonVoucher"
            >
              {{ $t('编辑') }}
            </el-button>
          </el-form-item>-->
          <el-form-item>
            <el-button
              type="primary"
              plain
              round
              icon="el-icon-delete"
              @click="handleBatchRemoveCommonVoucher"
            >
              {{ $t('删除') }}
            </el-button>
          </el-form-item>
        </div>
      </el-form>
      <gever-table
        ref="_commonVoucherRef"
        v-loading="commonVoucherLoading"
        :columns="commonVoucherTableColumns"
        :data="commonVoucherTable.dataList"
        :total="commonVoucherTable.total"
        :pagi="commonVoucherQueryParams"
        :height="400"
        @p-current-change="loadCommonVoucherList"
        @size-change="loadCommonVoucherList"
      >
        <template #operation="{ row }">
          <el-button type="text" @click="handleViewCommonVoucher(row)">
            {{ $t('查看') }}
          </el-button>
          <el-button type="text" @click="handleEditCommonVoucher(row)">
            {{ $t('编辑') }}
          </el-button>
          <el-button type="text" @click="handleRemoveCommonVoucher(row, false)">
            {{ $t('删除') }}
          </el-button>
        </template>
      </gever-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">{{ $t('关 闭') }}</el-button>
        <el-button type="primary" @click="handleChooseCommonVoucher">
          {{ $t('确 定') }}
        </el-button>
      </span>
    </el-dialog>

    <!-- 常用凭证查看、编辑、存档 -->
    <el-dialog
      :title="$t(commonVoucherInfoTitle)"
      :visible.sync="commonVoucherInfoVisible"
      append-to-body
      width="400px"
      custom-class="current-common-voucher-info"
    >
      <el-form
        ref="_commonVoucherInfoRef"
        :model="currentCommonVoucherInfo"
        :rules="commonVoucherInfoRules"
        :disabled="commonVoucherLoadType === 'view'"
        label-width="120px"
      >
        <el-form-item :label="$t('常用凭证名称')" prop="voucherName">
          <gever-input v-model="currentCommonVoucherInfo.voucherName" />
        </el-form-item>
        <el-form-item :label="$t('排序号')" prop="sortNumber">
          <el-input-number
            v-model="currentCommonVoucherInfo.sortNumber"
            :min="1"
            :controls="false"
            :max="99999999"
          />
        </el-form-item>
      </el-form>
      <span
        v-if="commonVoucherLoadType !== 'view'"
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="commonVoucherInfoVisible = false">
          {{ $t('关 闭') }}
        </el-button>
        <el-button type="primary" @click="handleSaveCommonVoucher">
          {{ $t('保 存') }}
        </el-button>
      </span>
    </el-dialog>

    <!-- 确认凭证导入 -->
    <el-dialog :visible.sync="confirmVisible" append-to-body width="400px">
      <div>
        <i
          class="el-icon-warning-outline"
          style="color: #fcdf5b; font-size: 22px; vertical-align: middle"
        />
        <span>{{ $t('您确定导入常用凭证吗？') }}</span>
      </div>
      <el-checkbox v-model="replaceFlag">
        {{ $t('智能摘要替换为') }}
      </el-checkbox>
      <br />
      <el-date-picker
        v-model="replaceDate"
        :disabled="!replaceFlag"
        type="month"
        placeholder=""
        :editable="false"
        value-format="yyyy-MM"
        :clearable="false"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="confirmVisible = false">{{ $t('关 闭') }}</el-button>
        <el-button type="primary" @click="handleConfirmImportVoucher">
          {{ $t('确 定') }}
        </el-button>
      </span>
    </el-dialog>
    <!--新增项目-->
    <el-dialog
      :title="'新增辅助核算项目'"
      width="50%"
      :visible.sync="accountingVisible"
      @close="$refs['_accountingRef'].resetFields()"
    >
      <el-form
        ref="_accountingRef"
        label-width="90px"
        :model="accountingForm"
        :rules="accountingRules"
      >
        <el-form-item prop="assistTypeName" label="类型名称">
          <gever-input
            v-model="accountingForm.assistTypeName"
            :maxlength="100"
            disabled
          />
        </el-form-item>
        <el-form-item prop="projectCode" label="项目编号">
          <gever-input
            v-model="accountingForm.projectCode"
            :maxlength="10"
            show-word-limit
            type="number"
          />
        </el-form-item>
        <el-form-item prop="projectName" label="项目名称">
          <gever-input
            v-model="accountingForm.projectName"
            :maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item prop="abbr" label="简称">
          <gever-input
            v-model="accountingForm.abbr"
            :maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item prop="indexs" label="序号">
          <el-input-number
            v-model="accountingForm.indexs"
            style="width: 100%"
            :min="1"
            :step="1"
            :controls="true"
            :max="999999"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item prop="remark" label="备注">
          <gever-input
            v-model="accountingForm.remark"
            type="textarea"
            :rows="4"
            resize="none"
            :maxlength="250"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="accountingVisible = false">
          {{ $t('关闭') }}
        </el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ $t('保存') }}
        </el-button>
      </template>
    </el-dialog>
    <!-- 选择会计科目 -->
    <el-dialog
      v-if="subVisible"
      :title="subTitle"
      :visible.sync="subVisible"
      width="800px"
      class="subject-dialog"
      :before-close="handleCloseDialog"
    >
      <subjectComponent
        v-if="dialogTitle"
        style="height: 500px"
        :table-show="false"
        @handleSubjectData="handleSubjectData"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDialog">
          {{ $t('关 闭') }}
        </el-button>
        <el-button type="primary" @click="submitSubject">
          {{ $t('确 定') }}
        </el-button>
      </span>
    </el-dialog>

    <el-dialog
      :title="$t('使用说明')"
      :visible.sync="voucherHelpVisible"
      width="1000px"
      top="5vh"
      append-to-body
    >
      <voucher-help style="height: 500px"></voucher-help>
    </el-dialog>
    </gever-dialog></div>
</template>

<script>
import { getMaxProjectCode } from '@/api/financial/setting/base-data/assist-project'
import { orgBooksPeriod } from '@/api/financial/common.js'
import {
  voucherAddInit,
  voucherEditInit,
  getModelSummaryList,
  getModelItemList,
  cashiersettlementtype,
  loadVoucher,
  loadSummaryList,
  loadSummaryInfo,
  saveSummaryInfo,
  removeSummaryInfo,
  removeBatchRemoveSummaryInfo,
  getAssistForVoucher,
  mAccountingVoucher,
  getMAccountingVoucher,
  loadMAccountingVoucher,
  saveMAccountingVoucher,
  deleteMAccountingVoucher,
  saveVoucher,
  getPeriodBalance,
  getPeriodPnl,
  loadWriteOff,
  carryoverVoucher,
  customCarryoverVoucher,
  voucherCopy,
  batchSave
} from '@/api/financial/accounting/voucher.js'
import { save } from '@/api/financial/setting/base-data/assist-project.js'
import { generateVoucher } from '@/api/financial/asset/common.js'
import Subject from '@/views/financial/setting/base-data/accounting-item/index.vue'
import { comma } from '@/utils/gever.js'
import { createNamespacedHelpers } from 'vuex'
import subjectComponent from '@/views/financial/setting/base-data/accounting-item/components/subject-component.vue'
import voucherHelp from '@/views/financial/components/voucher/help.vue'
const { mapState } = createNamespacedHelpers('financial')
export default {
  components: { Subject, subjectComponent, voucherHelp },
  props: {
    originId: { type: String, default: '' }, // 初始获取的凭证id
    voucherList: { type: Array, default: () => [] }, // 结转分录数据
    dialogTitle: { type: String, default: '' },
    dialogVisible: { type: Boolean, default: false },
    showTopBtns: { type: Boolean, default: true },
    appendToBody: { type: Boolean, default: false },
    voucherCopy: { type: Boolean, default: false },
    dialogName: { type: String, default: '' },
    noPager: {
      type: String,
      default: '0',
      validator: (noPager) => ['0', '1'].includes(noPager)
    }, // 是否不显示切换凭证按钮（0:显示，1:不显示）
    loadType: {
      type: String,
      default: 'view',
      validator: (type) =>
        [
          'add',
          'edit',
          'view',
          'generate',
          'writeOff',
          'carryover',
          'customCarryover',
          'receive'
        ].includes(type)
    }, // add、edit、view、generate
    generateModule: {
      type: String,
      default: 'assetPosting'
    }, // 生成凭证的模块
    generateIds: {
      type: String,
      default: ''
    },
    writeOffIds: {
      type: String,
      default: ''
    },
    carryoverId: {
      type: String,
      default: ''
    },
    mergeItem: {
      type: Number,
      default: 0,
      validator: (type) => [0, 1].includes(type)
    }
  },
  data() {
    return {
      curKey: null,
      editStatus: '',
      vxeShow: true,
      subVisible: false,
      subjectData: {},
      currentI: {},
      subTitle: '',
      vxeFzShow: true,
      loadTypeLocal: '',
      id: '', // 当前凭证id
      editPage: false,
      visible: true,
      isSave: false,
      subjectPeriodBalanceList: [], // 科目余额列表
      subjectPeriodPnlList: [], // 科目实际发生额列表
      summaryList: [], // 摘要列表
      subjectList: [], // 科目列表
      currentSubjectList: [],
      cashierSettlementType: [], // 结算方式列表

      booksId: this.$store.state.financial.books.id, // 账套id
      booksName: this.$store.state.financial.books.booksName,
      period: this.$store.state.financial.books.period,
      createYear: this.$store.state.financial.books.createYear,
      no_pager: '0',
      periodBegin: '', // 当前期间开始日期
      periodEnd: '', // 当前期间结束日期
      showSubBalInRow: '', // 是否显示行余额（0:不显示，1:显示）
      subjectInputFocus: true, // 科目输入框是否聚焦
      subBalInRowMap: new Map(), // 行内科目余额map，key:科目id，value:科目余额（科目记账余额+科目未记账凭证发生额）
      booksPeriod: [],
      loading: false, // 凭证页面loading
      voucher: {
        receiptSheets: 0, // 附单据数
        voucherName: '', // 凭证字
        voucherNumber: '', // 凭证号
        creatorName: '', // 制单人
        businessDate: '',
        entryList: []
      }, // 凭证信息
      summaryLoading: false, // 摘要列表loading
      summaryVisible: false, // 常用摘要dialog
      summaryTableColumns: [
        { type: 'selection', width: '50' },
        { type: 'index', label: '序号', width: '80' },
        { label: '编号', prop: 'code' },
        { label: '摘要', prop: 'summary' },
        { label: '操作', slotName: 'operation', width: '150' }
      ],
      summaryQueryParams: {
        // 摘要列表查询参数
        booksId: '',
        summary: '',
        page: 1,
        rows: 20
      },
      summaryTable: {
        dataList: [],
        total: 0
      },

      summaryInfoLoading: false, // 摘要信息loading
      summaryInfoTitle: '', // 摘要信息dialog
      summaryInfoVisible: false,
      isSummaryInfoView: true,
      summaryInfo: {},
      summaryInfoRules: {
        code: [{ required: true, message: '请输入编号', trigger: 'blur' }],
        summary: [{ required: true, message: '请输入摘要', trigger: 'blur' }]
      },

      subjectVisible: false, // 会计科目维护

      assistVisible: false,
      assistList: [], // 辅助核算
      assistArrList: [], // 初始辅助核算
      currentEntry: {}, // 当前编辑的分录
      cashierFlag: 0,

      importVisible: false,
      confirmVisible: false,
      commonVoucherLoading: false,
      commonVoucherQueryParams: {
        voucherName: '',
        booksId: '',
        period: '',
        page: 1,
        rows: 20
      },
      commonVoucherTable: {
        dataList: [],
        total: 0
      },
      popoverShow: false,
      commonVoucherTableColumns: [
        { type: 'selection', width: '50' },
        // { type: 'index', label: '序号', width: '80' },
        // { prop: 'index', label: '序号', width: '80' },
        { label: '常用凭证名称', prop: 'voucherName', width: '150' },
        { label: '日期', prop: 'businessDate' },
        { label: '附单据', prop: 'receiptSheets', width: '100' },
        { label: '排序号', prop: 'sortNumber' },
        { label: '操作', slotName: 'operation', width: '150' }
      ],
      replaceFlag: false,
      replaceDate: '',
      selectedCommonVoucher: [],

      commonVoucherInfoTitle: '',
      commonVoucherInfoVisible: false, // 常用凭证编辑、查看
      commonVoucherLoadType: '', // view，edit，add
      currentCommonVoucherInfo: {},
      accountingVisible: false, // 新增项目
      accountingRules: {
        projectName: [
          { required: true, message: '项目名称必填', trigger: 'blur' }
          // {
          //   pattern: /^[A-Za-z0-9\u4e00-\u9fa5]*$/,
          //   message: '只能输入中文英文和数字',
          //   trigger: ['blur', 'change']
          // }
        ],
        projectCode: [
          { required: true, message: '项目编号必填', trigger: 'blur' },
          {
            pattern: /^[A-Za-z0-9\u4e00-\u9fa5]*$/,
            message: '只能输入中文英文和数字',
            trigger: ['blur', 'change']
          }
        ],
        indexs: [{ required: true, message: '序号必填', trigger: 'blur' }]
      },
      accountingForm: {
        indexs: undefined,
        projectName: '',
        assistTypeName: '',
        projectCode: '',
        abbr: '',
        remark: ''
      },
      accountingRow: {},
      accountingKey: null,
      commonVoucherInfoRules: {
        voucherName: [
          { required: true, message: '请输入常用凭证名称', trigger: 'blur' }
        ],
        sortNumber: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      },
      list: [],
      focusSubjectId: '', // 聚焦的科目id
      assistChange: true,
      selectAssistFlag: false,
      curItemId: '',
      voucherHelpVisible: false
    }
  },
  computed: {
    ...mapState(['books']),
    currentSubjectBalance() {
      let res = null
      const currentSubjectBalance = this.subjectPeriodBalanceList.find(
        (item) => item.accountingItemId === this.currentEntry.accountingItemId
      )
      if (currentSubjectBalance) {
        res = currentSubjectBalance.endBal ? currentSubjectBalance.endBal : 0
      } else {
        res = 0
      }
      return comma(res, 2)
    },
    allEndBal() {
      let res = null
      const currentSubjectBalance = this.subjectPeriodBalanceList.find(
        (item) =>
          item.accountingItemId === this.curItemId &&
          item.periodIndex == (this.voucher?.period || this.period)
      )
      // const currentSubjectBalance = this.subjectPeriodBalanceList.find((item) => item.accountingItemId === this.currentEntry.accountingItemId)
      if (currentSubjectBalance) {
        res = currentSubjectBalance.allEndBal
          ? currentSubjectBalance.allEndBal
          : 0
      } else {
        res = 0
      }
      return comma(res, 2)
    },
    debitTotalAmount() {
      return this.voucher.entryList.reduce((sum, value) => {
        const debit = Number(value.debit) ? Number(value.debit) : 0
        return this.accAdd(sum, debit)
      }, 0)
    },
    creditTotalAmount() {
      return this.voucher.entryList.reduce((sum, value) => {
        const credit = Number(value.credit) ? Number(value.credit) : 0
        return this.accAdd(sum, credit)
      }, 0)
    },
    amountToDottedString() {
      return function (amount) {
        // 金额转换为字符串，1314转换为'1,314.00'
        const result = isNaN(comma(amount, 2)) ? '' : comma(amount, 2)
        return result
      }
    },
    assistComplete(item) {
      return function (item) {
        let complete = true
        for (let i = 0; i < item.assistArr.length; i++) {
          complete = complete && item.assistArr[i]
        }
        if (complete && item.assistArr.length) {
          item.assistProjectId = item.assistArr
            .reduce((sum, value) => {
              return sum + value.id + ','
            }, '')
            .slice(0, -1)
          item.assistName = item.assistArr.reduce((sum, value) => {
            return sum + '_' + value.name
          }, '')
          item.assistTypeId = item.assistArr
            .reduce((sum, value) => {
              return sum + value.assistTypeId + ','
            }, '')
            .slice(0, -1)
        }
        return Boolean(complete)
      }
    }
  },
  watch: {
    // accountingVisible:{
    //   handler(){
    //     this.$emit('close')
    //   }
    // },
    currentEntry: {
      /**
       * 根据当前行中的会计科目，判断底部结算方式、结算号、结算日期是否可编辑
       * 0不可编辑，1可编辑
       */
      handler: function (val) {
        const currentSubjectId = val.accountingItemId
        const currentSubject = this.subjectList.find(
          (subject) => subject.id === currentSubjectId
        )
        if (!currentSubjectId || !currentSubject) {
          this.cashierFlag = 0
        } else {
          this.cashierFlag = currentSubject.cashierFlag
        }
      },
      deep: true
    },
    currentSubjectList: {
      handler: function (val) {
        if (!val || !val.length) {
          this.focusSubjectId = ''
        } else {
          this.focusSubjectId = val[0].id
        }
      },
      deep: true
    },
    curItemId(val) {
      const findRow = this.subjectPeriodBalanceList.find(
        (cur) =>
          cur.accountingItemId === val &&
          cur.periodIndex == (this.voucher?.period || this.period)
      )
      if (!findRow && val) {
        this.handleGetPeriodBalance()
      }
    }
  },
  created() {
    this.initBaseConfigData()
  },
  mounted() {
    this.assistChange = true
    this.loadTypeLocal = this.loadType
    const voucher = document.getElementsByClassName('voucher')[0]
    voucher.click()
    if (voucher) {
      voucher.onclick = (e) => {
        const assistForm = document.getElementsByClassName('assist-form')
        // const
        const subjectCell = document.getElementsByClassName('subject-cell')
        const subjectList = document.getElementsByClassName('subject-list')
        this.$nextTick(() => {
          setTimeout(() => {
            this.voucher.entryList.forEach((item, key) => {
              this.handleAssistChange(item, key, '', '', 'click')
              if (
                item.subjectVisible &&
                !subjectList[key]?.contains(e.target) &&
                !subjectCell[key]?.contains(e.target)
              ) {
                item.subjectVisible = false
              }
            })
          }, 1000)
        })
      }
    }
    document.onkeydown = (e) => {
      if (e.ctrlKey && e.keyCode === 83) {
        // Ctrl+s 保存凭证
        e.preventDefault()
        this.handleSaveVoucher()
      } else if (e.ctrlKey && e.keyCode === 88) {
        // Ctrl+x 关闭凭证页面
        // e.preventDefault()
        // this.handleClose()
      }
    }
    if (this.loadType === 'add' && this.originId) {
      this.getBooksPeriod()
      // this.$set(this.voucher, 'period', this.books.period)
    }
  },
  // 分录来源不为0（手工）时，需要对凭证编辑作控制

  // source字段说明：
  //   //内部业务(CODE范围：1-20)
  //   WRITE_OFF(0, "冲销", false, 1, "/financial/accounting/accountingVoucher"),
  //   CARRY_OVER(1, "结转损益", false, 0, ""),
  //   CASHIER_ACCOUNTING(2, "会计分单", false, 1, "/financial/accounting/business/accountingDivides"),
  //   ASSET_ADD(3, "资产增加", true, 1, "/financial/asset/assetPosting"),
  //   ASSET_DEPRECIATION(4, "资产折旧", true, 1, "/financial/asset/assetDepreciation"),
  //   ASSET_ADJUST(5, "资产变动", true, 1, "/financial/asset/assetAdjust"),
  //   ASSET_DISPOSE(6, "资产处置", true, 1, "/financial/asset/assetDispose"),

  //   //外部业务(CODE范围：21-62)
  //   COOP_MEMBER_FUNDING(21, "成员出资", "accountingCoopMemberFunding", "acctPostCoopMembFund"),
  //   COOP_FINANCIAL_AID_ALLOT(22, "财政补助资金", "accountingCoopFinancialAidAllot", "acctPostCoopFinaAid"),
  //   COOP_DONATED_PROPERTY_ALLOT(23, "捐赠财产", "accountingCoopDonatedPropertyAllot", "acctPostCoopDonaProp"),
  //   COOP_PURCHASE(24, "采购入库", "accountingCoopPurchase", "acctPostCoopPurchase"),
  //   COOP_SALE(25, "销售出库", "accountingCoopSale", "acctPostCoopSale"),
  //   COOP_SURPLUS_ALLOT(26, "盈余分配", "accountingCoopSurplusAllot", "acctPostCoopSurpAllo"),
  //   PAYMENT_BANK_STATEMENT(27, "银行流水", "accountingPaymentBankStatement", "acctPostPayBankState"),
  //   BILL(28, "票据收款", "accountingBill", "acctPostBill"),
  //   CONTRACT_COLLECTION(29, "合同收款", "accountingContractCollection", "acctPostContractCollection"),
  //   CONTRACT_PAYMENT(30, "合同付款", "accountingContractPayment", "acctPostContractPayment");
  methods: {
    submitSubject() {
      if (this.subjectData?.attributes?.seal) {
        return this.$message.warning('不能选择已封存的科目')
      }
      if (this.subjectData.state && this.subjectData.state == 'closed') {
        this.$message.warning(this.$t('请选择明细科目！'))
        return
      }
      const subject = this.subjectList.find(
        (item) => item.id === this.subjectData.id
      )
      if (subject) {
        this.handleSelectSubject(subject, this.currentI, this.curKey)
      }
      this.handleCloseDialog()
      this.curKey = null
    },
    handleSubjectData(data) {
      this.subjectData = data
    },
    handleCloseDialog() {
      this.subVisible = false
      this.subTitle = ''
    },
    handleAddSubject(item) {
      this.currentI = item
      this.subVisible = true
      this.subTitle = this.$t('账套-会计科目选择')
    },
    changePeriod(val) {
      this.voucherAddInit(val)
      this.handleGetPeriodPnl(val)
      if (this.voucher.entryList.length) {
        this.voucher.entryList.forEach((cur) => {
          this.handleGetPeriodBalance(val, cur.accountingItemId)
        })
      }
    },
    async getBooksPeriod() {
      const { data: periods = [] } = await orgBooksPeriod({
        orgId: this.books.orgId,
        year: this.books.createYear
      })
      this.booksPeriod = periods
    },
    handleSubjectVisible(key) {
      this.$refs['xDown2'][key].hidePanel()
      this.subjectVisible = true
    },
    handleSubjectVisiblInner(key, assistIndex) {
      this.$refs['assistPulldown' + key + assistIndex][0].hidePanel()
      this.subjectVisible = true
    },
    handleTop(key) {
      // 上移
      // this.voucher.entryList

      if (key == 0) {
        this.$message.warning(this.$t('顶行无法上移!'))
        return
      }
      const topData = this.voucher.entryList[key - 1]
      this.$set(this.voucher.entryList, key - 1, this.voucher.entryList[key])
      this.$set(this.voucher.entryList, key, topData)
    },
    handleBottom(key) {
      // 下移

      if (key == this.voucher.entryList.length - 1) {
        this.$message.warning(this.$t('底行无法下移!'))
        return
      }
      const bottomData = this.voucher.entryList[key + 1]
      this.$set(this.voucher.entryList, key + 1, this.voucher.entryList[key])
      this.$set(this.voucher.entryList, key, bottomData)
    },
    handleCloseDir(add) {
      if (
        this.isSave ||
        this.editStatus === JSON.stringify(this.voucher.entryList)
      ) {
        this.handleClose(add)
      } else {
        this.$confirm('你当前有未保存的凭证，是否关闭当前界面', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.handleClose(add)
          })
          .catch(() => {
            // this.$message({
            //   type: 'info',
            //   message: '已取消关闭'
            // });
            return
          })
      }
    },
    handleAccountingItemInput(e, item) {
      if (this.loadType !== 'view') {
        this.curItemId = item.accountingItemId
      }
      const res = e ? e.replace('undefined', '') : e
      if (item.accountingItemId) return
      const index = this.voucher.entryList.indexOf(item)
      this.$set(
        this.voucher.entryList[index],
        'accountingItemInputShowName',
        res
      )
      this.currentSubjectList = this.subjectList.filter((subject) => {
        if (e.trim()) {
          return subject.text.includes(e)
        } else {
          return subject
        }
      })
      this.$nextTick(() => {
        this.vxeShow = false
        setTimeout(() => {
          this.vxeShow = true
        }, 0)
      })
    },
    handleAccountingItemFocus(item, key) {
      this.currentSubjectList = this.subjectList
      this.curItemId = item.accountingItemId
    },
    handleAccountingItemBlur(item, key) {
      if (!item?.accountingItemId) {
        this.handleRechooseSubject(item, key)
      }
      this.subjectInputFocus = true
      const index = this.voucher.entryList.indexOf(item)
      this.$set(
        this.voucher.entryList[index],
        'accountingItemInputShowName',
        ''
      )
    },
    handleClick(item, key) {
      if (this.loadType === 'view' || this.subjectEditLimit(item)) return
      this.subjectInputFocus = false // 聚焦时显示科目余额，另外还需showSubBalInRow控制
      // 点击会计科目cell时判断弹出科目列表还是辅助科目列表
      console.log(item, item.assistFlag, key, 'item.assistFlag')
      this.curKey = key
      if (item.assistFlag) {
        this.handleShowAssist(item.accountingItemId, item, key)
      } else {
        item.subjectVisible = true

        this.$refs.xDown2[key].showPanel()
        this.focusSubjectId = this.currentSubjectList[0].id
      }
    },
    subjectEditLimit(item) {
      return Boolean(item.source && this.containBusiness(item.source, 2))
    },
    disabledDate(date) {
      return (
        date.getTime() > new Date(this.periodEnd).getTime() ||
        date.getTime() < new Date(this.periodBegin + ' 00:00:00').getTime()
      )
    },
    handleAssistChange(item, key, e, assistIndex, type) {
      if (type !== 'click' && type !== 'noSearch') {
        this.selectAssistFlag = false
        this.voucher.entryList[key].assistArr[assistIndex] = e
        const list = this.assistArrList[assistIndex].assistPrjList
        const arr = list.filter(
          (c) => (c.code.toString() + '-' + c.name.toString()).indexOf(e) > -1
        )
        // this.assistList[assistIndex].assistPrjList = arr
        // this.assistList[assistIndex].assistPrjList = list
        this.assistList[assistIndex].assistPrjList = e ? arr : list
        this.$nextTick(() => {
          this.vxeFzShow = false
          setTimeout(() => {
            this.vxeFzShow = true
          }, 0)
        })
      } else if (type == 'click') {
        console.log(2222)
        this.selectAssistFlag = true
      }
      this.$forceUpdate()
      const currentIndex = this.voucher.entryList.indexOf(item)
      console.log(currentIndex, 'currentIndex')
      if (this.assistComplete(item)) {
        if ((e && typeof e !== 'object') || !this.assistChange) {
          this.assistChange = false
          // return this.$message.warning(this.$t('请选择正确的辅助核算项目'))
        } else {
          this.$set(this.voucher.entryList[key], 'assistVisible', false)
          console.log(this.voucher.entryList[key], 'key')
        }
      } else {
        this.$set(this.voucher.entryList[currentIndex], 'assistVisible', true)
        const assistMask = document.getElementById('assist-mask')
        if (assistMask) {
          assistMask.onclick = (e) => {
            this.$confirm(this.$t('当前科目不完整，确认放弃当前科目操作吗?'), {
              confirmButtonText: this.$t('确定'),
              cancelButtonText: this.$t('取消'),
              type: 'warning'
            })
              .then(() => {
                this.handleRechooseSubject(item, key)
                assistMask.style.display = 'none'
              })
              .catch((err) => {})
          }
        }
      }
    },
    handleAssistBlur(item, key, e, assistIndex) {
      const list = this.assistArrList[assistIndex].assistPrjList
      const arr = list.findIndex(
        (c) => c.code.toString() + '-' + c.name.toString() === e
      )
      if (arr !== -1) {
        return
      } else {
        setTimeout(() => {
          if (!this.selectAssistFlag) {
            this.$set(this.voucher.entryList[key].assistArr, assistIndex, '')
            this.$set(
              this.assistList[assistIndex],
              'assistPrjList',
              this.assistArrList[assistIndex].assistPrjList
            )
            return this.$message.warning(this.$t('请选择正确的辅助核算项目'))
            // this.voucher.entryList[key].assistArr[assistIndex] = ''
            // this.assistList[assistIndex].assistPrjList =
            //   this.assistArrList[assistIndex].assistPrjList
          }
        }, 200)
      }
    },
    hidePanel(item, key, $event, assistIndex, type) {
      this.handleAssistChange(item, key, $event, assistIndex, type)
    },
    handleSelectSubject(val, item, key, flag) {
      if (val.leaf === 1) return
      // this.curItemId = item.accountingItemId
      const currentSubject = this.subjectList.find((item) => item.id === val.id)
      const currentIndex = this.voucher.entryList.indexOf(item)
      this.$set(
        this.voucher.entryList[currentIndex],
        'accountingItemInputShowName',
        ''
      )

      if (currentSubject.assistFlag) {
        this.$set(this.voucher.entryList[currentIndex], 'assistVisible', true)
        this.handleShowAssist(val.id, item, key)
      } else {
        this.$set(this.voucher.entryList[currentIndex], 'assistVisible', false)
      }
      // this.$refs['_subjectRef'][key].doClose()

      if (flag) {
        this.$refs.xDown2[key].hidePanel()
      }
      this.$set(
        this.voucher.entryList[currentIndex],
        'assistFlag',
        val.assistFlag
      )
      this.$set(
        this.voucher.entryList[currentIndex],
        'accountingItemId',
        val.id
      )
      this.$set(
        this.voucher.entryList[currentIndex],
        'accountingItemShowName',
        val.name
      )
      this.refreshSubjectBalInRow()
    },
    focusAssistArr(key, assistIndex, item) {
      if (!item.assistArr[assistIndex]) {
        this.selectAssistFlag = false
      }
      const sum = 'assistPulldown' + key + assistIndex
      this.$refs[`${sum}`][0].showPanel()
    },
    getValue(item, assistIndex) {
      if (item.assistArr[assistIndex] !== undefined) {
        if (item.assistArr[assistIndex]?.code) {
          return (
            item.assistArr[assistIndex].code.toString() +
            '-' +
            item.assistArr[assistIndex].name.toString()
          )
        } else {
          return item.assistArr[assistIndex]
        }
      } else {
        return ''
      }
      // return item.assistArr[assistIndex] !== undefined && item.assistArr[assistIndex]?.code ? item.assistArr[assistIndex].code.toString() +
      //                           '-' +
      //                           item.assistArr[assistIndex].name.toString() : ''
    },
    handleselectAssistItem(assistItem, item, key, assistIndex) {
      const currentIndex = this.voucher.entryList.indexOf(item)
      this.voucher.entryList[currentIndex].assistArr[assistIndex] = assistItem
      this.assistChange = true
      const sum = 'assistPulldown' + key + assistIndex
      this.$refs[`${sum}`][0].hidePanel()
      this.handleAssistChange(
        this.voucher.entryList[currentIndex],
        key,
        '',
        '',
        'click'
      )
    },
    handleShowAssist(accountingItemId, item, key) {
      const params = {
        booksId: this.booksId,
        accountingItemId: accountingItemId
      }
      getAssistForVoucher(params)
        .then((res) => {
          this.assistList = JSON.parse(JSON.stringify(res.data))
          this.assistArrList = JSON.parse(JSON.stringify(res.data))
          item.assistVisible = true

          // 根据assistProjectId判断是否回显辅助会计科目
          if (!item.assistArr.length) {
            item.assistArr = new Array(this.assistList.length)
            if (item.assistProjectId) {
              item.assistArr = []
              const assistProjectIdArr = item.assistProjectId.split(',')
              const assistTypeIdArr = item.assistTypeId.split(',')

              assistTypeIdArr.forEach((type, typeIndex) => {
                const currentAssitType = this.assistList.find(
                  (assist) => assist.id === type
                )
                if (currentAssitType) {
                  item.assistArr.push(
                    currentAssitType.assistPrjList.find(
                      (assistPr) =>
                        assistPr.id === assistProjectIdArr[typeIndex]
                    )
                  )
                }
              })
            }
          }
        })
        .finally(() => {
          const currentIndex = this.voucher.entryList.indexOf(item)
          const assistMask = document.getElementById('assist-mask')
          // eslint-disable-next-line no-empty
          if (this.assistComplete(item)) {
          } else {
            this.$set(
              this.voucher.entryList[currentIndex],
              'assistVisible',
              true
            )
            assistMask.onclick = (e) => {
              this.$confirm(
                this.$t('当前科目不完整，确认放弃当前科目操作吗?'),
                {
                  confirmButtonText: this.$t('确定'),
                  cancelButtonText: this.$t('取消'),
                  type: 'warning'
                }
              )
                .then(() => {
                  this.handleRechooseSubject(item, key)
                  assistMask.style.display = 'none'
                })
                .catch((err) => {})
            }
          }
        })
    },
    handleRechooseSubject(item, key) {
      const currentIndex = this.voucher.entryList.indexOf(item)
      this.$set(this.voucher.entryList[currentIndex], 'accountingItemId', '')
      this.$set(this.voucher.entryList[currentIndex], 'assistFlag', 0)
      this.$set(
        this.voucher.entryList[currentIndex],
        'accountingItemShowName',
        ''
      )
      this.$set(this.voucher.entryList[currentIndex], 'assistName', '')
      this.$set(this.voucher.entryList[currentIndex], 'assistProjectId', '')
      this.$set(this.voucher.entryList[currentIndex], 'assistTypeId', '')
      this.$set(this.voucher.entryList[currentIndex], 'assistVisible', false)
      this.$set(
        this.voucher.entryList[currentIndex],
        'assistArr',
        new Array(this.voucher.entryList[currentIndex].assistArr.length)
      )
      // this.$refs['_subjectRef'][key].doShow()
    },
    handleEnter() {
      try {
        this.currentEntry = this.voucher.entryList[0]
        this.$refs['_inputItem' + 0][0].focus()
      } catch (error) {}
    },
    // eslint-disable-next-line complexity
    handleShortcutKeyEvent(e, item, key) {
      // 摘要列取消上下左右箭头事件
      if (Number.isInteger(key / 4) && [37, 38, 39, 40].includes(e.keyCode)) {
        return
      }
      // 处理快捷键事件
      /**
       * keyCode  13  enter
       * keyCode  9   tab
       * keyCode  38  ArrowUp
       * keyCode  40  ArrowDown
       * keyCode  37  ArrowLeft
       * keyCode  39  ArrowRight
       * keyCode  8   删除
       */
      // if (item.accountingItemId) {
      //   this.$refs['xDown2'][item.index - 1].showPanel()
      // }
      if (![13, 9, 38, 40, 37, 39, 8].includes(e.keyCode)) {
        return
      }
      if (e.keyCode === 8 && key % 4 === 1) {
        const index = this.voucher.entryList.indexOf(item)

        // this.$set(this.voucher.entryList[index], 'accountingItemInputShowName', '')
        this.$set(this.voucher.entryList[index], 'accountingItemShowName', '')
        this.$set(this.voucher.entryList[index], 'accountingItemId', '')
        this.$set(this.voucher.entryList[index], 'assistFlag', 0)
        this.$set(this.voucher.entryList[index], 'assistName', '')
        this.$set(this.voucher.entryList[index], 'assistArr', [])
        this.$set(this.voucher.entryList[index], 'assistVisible', false)
        this.$set(this.voucher.entryList[index], 'subjectVisible', true)
        this.handleRechooseSubject(item, key)
      }
      // if (e.keyCode === 13 || e.keyCode === 9 || e.keyCode === 39) {
      if (e.keyCode === 13 || e.keyCode === 39) {
        // enter、tab键切换
        this.currentEntry = item
        // 会计科目列的enter键，只处理科目下拉列表的选择

        if ((e.keyCode === 13 || e.keyCode === 39) && key % 4 == 0) {
          this.$refs['_inputItem' + key][0].blur()
          this.handleClick(item, key / 4)
          const subject = this.subjectList.find(
            (a) => a.id === this.focusSubjectId
          )
          this.$refs['_inputItem' + (key + 1)][0].focus()
          if (!item.accountingItemShowName) {
            this.handleSelectSubject(subject, item, key / 4, false)
          }
        } else {
          switch (key % 4) {
            case 0:
              this.$refs['_inputItem' + key][0].blur()
              this.$refs['_inputItem' + (key + 1)][0].focus()
              // this.$nextTick(() => {
              //   this.$refs['_inputItem' + (key + 1)][0].focus()
              //   this.handleClick(item, key)
              // })
              break
            case 1:
              if (this.focusSubjectId) {
                const subject = this.subjectList.find(
                  (a) => a.id === this.focusSubjectId
                )
                if (!item.accountingItemShowName) {
                  this.handleSelectSubject(subject, item, key / 4, false)
                }
              }
              this.$refs['_inputItem' + key][0].blur()
              this.$refs.xDown2[(key - 1) / 4].hidePanel()
              this.$refs['_div' + (key + 1)][0].click()
              this.$nextTick(() => {
                this.curKey = null
              })
              break
            case 2:
              this.$refs['_div' + (key + 1)][0].click()
              break
            case 3:
              if (this.voucher.entryList.length > parseInt(key / 4 + 1)) {
                this.currentEntry =
                  this.voucher.entryList[parseInt(key / 4 + 1)]
                this.$refs['_inputItem' + (key + 1)][0].focus()
              } else {
                const addKey = (key + 1) / 4 - 1
                this.handleAdd(addKey)
                setTimeout(() => {
                  this.currentEntry =
                    this.voucher.entryList[parseInt(key / 4 + 1)]
                  this.$refs['_inputItem' + (key + 1)][0].focus()
                }, 1000)
              }
              break
            default:
              break
          }
        }
      } else if (e.keyCode === 38) {
        // 切换上一条分录
        if (key % 4 != 1) {
          if (parseInt(key / 4) === 0) return // 第一条分录无法向上切换
          this.currentEntry = this.voucher.entryList[parseInt(key / 4 - 1)]
        }
        let curFocusSubjectIndex = 0
        switch (key % 4) {
          case 0:
            this.$refs['_inputItem' + key][0].blur()
            this.$nextTick(() => {
              this.$refs['_inputItem' + (key - 4)][0].focus()
            })
            break
          case 1:
            // this.$refs['_inputItem' + key][0].blur()
            // this.$nextTick(() => {
            //   this.manualBlur()
            //   this.$refs['_inputItem' + (key - 4)][0].focus()
            //   this.handleClick(
            //     this.voucher.entryList[parseInt(key / 4 - 1)],
            //     key - 4
            //   )
            // })
            // 会计科目列不处理上下的切换，只处理科目下拉中的选中切换

            curFocusSubjectIndex = this.currentSubjectList.findIndex(
              (a) => a.id == this.focusSubjectId
            )
            if (
              curFocusSubjectIndex > -1 &&
              curFocusSubjectIndex < this.currentSubjectList.length - 1
            ) {
              curFocusSubjectIndex--
              if (curFocusSubjectIndex < 0) {
                curFocusSubjectIndex = 0
              }
              this.focusSubjectId =
                this.currentSubjectList[curFocusSubjectIndex].id
            }
            if (
              document.getElementsByClassName('isFocus')[0].offsetTop - 30 <=
              30
            ) {
              document.getElementsByClassName(
                'vxe-list--virtual-wrapper'
              )[0].scrollTop -= 30
            }
            break
          case 2:
          case 3:
            this.$refs['_div' + (key - 4)][0].click()
            break
          default:
            break
        }
      } else if (e.keyCode === 40) {
        // return
        // 切换下一条分录
        if (key % 4 != 1) {
          if (parseInt(key / 4) === this.voucher.entryList.length - 1) return // 最后一条分录无法向下切换
          this.currentEntry = this.voucher.entryList[parseInt(key / 4 + 1)]
        }
        let curFocusSubjectIndex = 0
        switch (key % 4) {
          case 0:
            this.$refs['_inputItem' + key][0].blur()
            this.$nextTick(() => {
              this.$refs['_inputItem' + (key + 4)][0].focus()
            })
            break
          case 1:
            // this.$refs['_inputItem' + key][0].blur()
            // this.$nextTick(() => {
            //   this.manualBlur()
            //   this.$refs['_inputItem' + (key + 4)][0].focus()
            //   this.handleClick(
            //     this.voucher.entryList[parseInt(key / 4 + 1)],
            //     key + 4
            //   )
            // })
            // 会计科目列不处理上下的切换，只处理科目下拉中的选中切换
            curFocusSubjectIndex = this.currentSubjectList.findIndex(
              (a) => a.id == this.focusSubjectId
            )
            if (
              curFocusSubjectIndex > -1 &&
              curFocusSubjectIndex < this.currentSubjectList.length - 1
            ) {
              curFocusSubjectIndex++
              if (curFocusSubjectIndex >= this.currentSubjectList.length) {
                curFocusSubjectIndex = this.currentSubjectList - 1
              }
              this.focusSubjectId =
                this.currentSubjectList[curFocusSubjectIndex].id
            }

            if (
              document.getElementsByClassName('subject-list')[0].clientHeight -
                document.getElementsByClassName('isFocus')[0].offsetTop <=
              120
            ) {
              document.getElementsByClassName(
                'vxe-list--virtual-wrapper'
              )[0].scrollTop += 30
            }
            break
          case 2:
          case 3:
            this.$refs['_div' + (key + 4)][0].click()
            break
          default:
            break
        }
      } else if (e.keyCode === 37) {
        // 切换到当前分录的前一个输入项
        this.currentEntry = item
        switch (key % 4) {
          case 0:
            if (parseInt(key / 4) === 0) {
              return // 第一条分录无法向前切换
            } else {
              this.currentEntry = this.voucher.entryList[parseInt(key / 4) - 1]
              this.$refs['_inputItem' + key][0].blur()
              this.$nextTick(() => {
                this.$refs['_div' + (key - 1)][0].click()
              })
            }
            break
          case 1:
            this.$refs['_inputItem' + key][0].blur()
            this.$nextTick(() => {
              this.manualBlur()
              this.$refs['_inputItem' + (key - 1)][0].focus()
              this.$refs.xDown2[(key - 1) / 4].hidePanel()
            })
            break
          case 2:
            this.$nextTick(() => {
              this.$refs['_inputItem' + (key - 1)][0].focus()
              this.handleClick(item, Math.floor(key / 4))
            })
            break
          case 3:
            this.$refs['_div' + (key - 1)][0].click()
            break
          default:
            break
        }
      }
    },
    manualBlur() {
      // 手动失焦，用于关闭各类popper
      const dom = document.getElementsByClassName('el-dialog__header')[0]
      dom.click()
    },
    focusNext(e, item, key) {
      this.currentEntry = item
      switch (key % 4) {
        case 0:
          this.$refs['_inputItem' + key][0].blur()
          this.$nextTick(() => {
            this.$refs['_inputItem' + (key + 1)][0].focus()
            this.handleClick(item, key)
          })
          break
        case 1:
          this.$refs['_inputItem' + key][0].blur()
          this.$refs['_div' + (key + 1)][0].click()
          break
        case 2:
          this.$refs['_div' + (key - 4)][0].click()
          break
        case 3:
          if (this.voucher.entryList.length > parseInt(key / 4 + 1)) {
            this.currentEntry = this.voucher.entryList[parseInt(key / 4 + 1)]
            this.$refs['_inputItem' + (key + 1)][0].focus()
          }
          break
        default:
          break
      }
    },
    limitDotted(value) {
      if (isNaN(value)) {
        value = null
      }
      if (value.indexOf('.') > 0) {
        value = value.slice(0, value.indexOf('.') + 3)
      }
    },
    async handleGetPeriodBalance(period, itemId) {
      const res1 = await getPeriodBalance({
        itemId: itemId || this.curItemId,
        booksId: this.booksId,
        period: period || this.period,
        addType: this.loadType === 'add' && this.originId ? 'copy' : ''
      })
      // this.subjectPeriodBalanceList = res1.data
      this.subjectPeriodBalanceList.push(...res1.data)
      this.subjectPeriodBalanceList.forEach((item) => {
        if (this.subBalInRowMap.has(item.accountingItemId)) {
          this.subBalInRowMap.set(
            item.accountingItemId,
            this.subBalInRowMap.get(item.accountingItemId) +
              (item.endBal ? Number(item.endBal) : 0)
          )
        } else {
          this.subBalInRowMap.set(
            item.accountingItemId,
            item.endBal ? Number(item.endBal) : 0
          )
        }
      })
    },
    async handleGetPeriodPnl(period) {
      const res2 = await getPeriodPnl({
        booksId: this.booksId,
        period: period || this.period,
        addType: this.loadType === 'add' && this.originId ? 'copy' : ''
      })
      this.subjectPeriodPnlList = res2.data
      this.subjectPeriodPnlList.forEach((item) => {
        if (this.subBalInRowMap.has(item.accountingItemId)) {
          this.subBalInRowMap.set(
            item.accountingItemId,
            this.subBalInRowMap.get(item.accountingItemId) +
              (item.pnl ? Number(item.pnl) : 0)
          )
        } else {
          this.subBalInRowMap.set(
            item.accountingItemId,
            item.pnl ? Number(item.pnl) : 0
          )
        }
      })
    },
    async initBaseConfigData() {
      this.loading = true
      this.handleGetPeriodPnl()
      const res3 = await getModelSummaryList({ booksId: this.booksId })
      this.summaryList = res3.data

      const res4 = await getModelItemList({ booksId: this.booksId })
      this.subjectList = res4.data
      this.currentSubjectList = res4.data

      const res5 = await cashiersettlementtype({ booksId: this.booksId })
      this.cashierSettlementType = res5.data

      this.initPage()
      // this.refreshSubjectBalInRow()
    },
    async getModelItemList() {
      const res = await getModelItemList({ booksId: this.booksId })
      this.subjectList = res.data
      this.currentSubjectList = res.data
    },
    async initPage() {
      this.id = this.originId
      if (this.loadType === 'add') {
        if (this.originId) {
          // 凭证复制
          // this.loadDataById()
          this.loading = true
          this.no_pager = this.noPager
          const res = await loadVoucher({
            id: this.id,
            booksId: this.booksId,
            noPager: this.no_pager
          })
          this.voucher = {
            ...res.data,
            period: this.voucher.period || this.books.period
          }

          this.voucher.entryList.forEach((entry) => {
            entry.assistArr = []
          })
          this.handlSetIndex()
          const tableBody = document.getElementById('table-body')
          if (tableBody) {
            tableBody.style.height =
              this.voucher.entryList.length >= 5
                ? '300px'
                : this.voucher.entryList.length * 60 + 'px'
          }
          this.currentEntry = this.voucher.entryList[0]
          this.loading = false

          const arr = await voucherAddInit({
            booksId: this.booksId,
            period: this.period,
            addType: this.loadType === 'add' && this.originId ? 'copy' : ''
            // type: this.loadType === 'add' && this.originId ? 'type' : ''
          })
          const myDate = new Date()
          const Y = myDate.getFullYear()
          const M = myDate.getMonth() + 1
          const D = myDate.getDate()
          const curDay = Y + '-' + M + '-' + D
          // this.voucher.businessDate = curDay
          this.voucher.businessDate = arr.businessDate
          this.voucher.voucherName = arr.voucherName
          this.voucher.voucherNumber = arr.voucherNumber
          this.voucher.id = ''
          this.voucher.bookkeeperName = ''
          this.voucher.checkerName = ''
          this.voucher.creatorName = arr.creatorName
          this.voucher.receiptSheets = arr.receiptSheets
          this.voucher.receiptSheets = arr.receiptSheets
          this.$set(this.voucher, 'deferMonthended', arr.deferMonthended)
          this.$set(this.voucher, 'period', arr.period)
          this.periodEnd = arr.periodEnd
          this.periodBegin = arr.periodBegin
          this.period = arr.period
          this.curItemId = this.voucher.entryList[0]?.accountingItemId
          // voucherAddInit({ booksId: this.booksId, period: this.period }).then((res) => {
          //   console.log(res.voucherNumber, '凭证字号1')
          //   this.voucher.voucherName = res.voucherName
          //   this.voucher.voucherNumber = res.voucherNumber
          //   this.voucher.id = ''
          //   this.voucher.creatorName = res.creatorName
          // })
        } else {
          voucherAddInit({ booksId: this.booksId, period: this.period })
            .then((res) => {
              this.voucher.accountingPeriodId = res.accountingPeriodId
              this.voucher.booksId = res.booksId
              this.voucher.creatorName = res.creatorName
              this.voucher.receiptSheets = res.receiptSheets
              this.voucher.voucherName = res.voucherName
              this.voucher.voucherNumber = res.voucherNumber
              this.voucher.businessDate = res.businessDate
              this.periodBegin = res.periodBegin
              this.periodEnd = res.periodEnd
              this.no_pager = res.noPager
              this.voucher.page = {}
              this.$set(this.voucher, 'deferMonthended', res.deferMonthended)
              this.periodBegin = res.periodBegin
              this.periodEnd = res.periodEnd
              this.loading = false
            })
            .finally(() => {
              this.voucher.entryList = [
                { index: 1, credit: 0, debit: 0, assistArr: [] },
                { index: 2, credit: 0, debit: 0, assistArr: [] },
                { index: 3, credit: 0, debit: 0, assistArr: [] },
                { index: 4, credit: 0, debit: 0, assistArr: [] },
                { index: 5, credit: 0, debit: 0, assistArr: [] }
              ]
              this.handlSetIndex()
              this.loading = false
              this.currentEntry = this.voucher.entryList[0]
              this.editStatus = JSON.stringify(this.voucher.entryList)
            })
        }
        // 新增凭证
      } else if (this.loadType === 'generate') {
        this.generateVoucherByRowId()
      } else if (this.loadType === 'writeOff') {
        this.writeOffVoucher() // 冲销
      } else if (this.loadType === 'carryover') {
        this.carryoverVoucher() // 结转损益
      } else if (this.loadType === 'customCarryover') {
        this.customCarryoverVoucher() // 自定义结转损益
      } else if (this.loadType === 'receive') {
        this.voucher.entryList = []
        // 结转应收款
        voucherAddInit({ booksId: this.booksId, period: this.period })
          .then((res) => {
            this.voucher.accountingPeriodId = res.accountingPeriodId
            this.voucher.booksId = res.booksId
            this.voucher.creatorName = res.creatorName
            this.voucher.receiptSheets = res.receiptSheets
            this.voucher.voucherName = res.voucherName
            this.voucher.voucherNumber = res.voucherNumber
            this.voucher.businessDate = res.businessDate
            this.periodBegin = res.periodBegin
            this.periodEnd = res.periodEnd
            this.no_pager = res.noPager
            this.voucher.page = {}
            this.periodBegin = res.periodBegin
            this.periodEnd = res.periodEnd
            this.loading = false
          })
          .finally(() => {
            this.voucher.entryList = this.voucherList
            this.voucher.entryList.forEach((entry) => {
              entry.assistArr = []
            })
            this.handlSetIndex()
            this.loading = false
            this.currentEntry = this.voucher.entryList[0]
            this.editStatus = JSON.stringify(this.voucher.entryList)
          })
      } else {
        // 查看凭证
        this.loadDataById()
      }
    },
    async voucherEditInit() {
      // 是否显示分录的科目余额
      const res = await voucherEditInit({
        booksId: this.booksId,
        period: this.period,
        id: this.voucher.id || ''
      })
      if (this.loadType === 'add' || this.loadType === 'view') {
        this.showSubBalInRow = '0'
      } else {
        this.showSubBalInRow = res.showSubBalInRow
      }
      this.editStatus = JSON.stringify(this.voucher.entryList)
      this.refreshSubjectBalInRow()
    },
    voucherAddInit() {
      // 凭证新增时的showSubBalInRow参数获取，包含计提折旧、冲销、结转、自定义结转
      // 手工新增已在initPge中单独处理
      voucherAddInit({ booksId: this.booksId, period: this.period }).then(
        (res) => {
          this.showSubBalInRow = res.showSubBalInRow
          this.loading = false
        }
      )
    },
    writeOffVoucher() {
      loadWriteOff({ booksId: this.booksId, id: this.writeOffIds }).then(
        (res) => {
          this.voucher = res.data
          this.voucher.entryList.forEach((entry) => {
            entry.assistArr = []
            entry.businessId = this.generateIds
            entry.debit =
              entry.debit ||
              (entry.direction === 1 ? entry.amount : entry.debit)
            entry.credit =
              entry.credit ||
              (entry.direction === 2 ? entry.amount : entry.credit)
          })
          this.handlSetIndex()
          this.period = res.data.accountingPeriodIndex // 冲销使用获取到的期间
          const tableBody = document.getElementById('table-body')
          if (tableBody) {
            tableBody.style.height =
              this.voucher.entryList.length >= 5
                ? '300px'
                : this.voucher.entryList.length * 60 + 'px'
          }
          this.currentEntry = this.voucher.entryList[0]
          this.refreshSubjectBalInRow()
          this.voucherAddInit()
        }
      )
    },
    carryoverVoucher() {
      carryoverVoucher({
        booksId: this.booksId,
        period: this.period,
        id: this.carryoverId
      }).then((res) => {
        this.voucher = res.data
        this.voucher.entryList.forEach((entry) => {
          entry.assistArr = []
          entry.businessId = this.generateIds
          entry.debit =
            entry.debit || (entry.direction === 1 ? entry.amount : entry.debit)
          entry.credit =
            entry.credit ||
            (entry.direction === 2 ? entry.amount : entry.credit)
        })
        this.handlSetIndex()
        this.curItemId = this.voucher.entryList[0]?.accountingItemId

        const tableBody = document.getElementById('table-body')
        if (tableBody) {
          tableBody.style.height =
            this.voucher.entryList.length >= 5
              ? '300px'
              : this.voucher.entryList.length * 60 + 'px'
        }
        this.currentEntry = this.voucher.entryList[0]
        this.refreshSubjectBalInRow()
        this.voucherAddInit()
      })
    },
    customCarryoverVoucher() {
      customCarryoverVoucher({
        booksId: this.booksId,
        period: this.period,
        id: this.carryoverId
      }).then((res) => {
        this.voucher = res.data
        this.voucher.entryList.forEach((entry) => {
          entry.assistArr = []
          entry.businessId = this.generateIds
          entry.debit =
            entry.debit || (entry.direction === 1 ? entry.amount : entry.debit)
          entry.credit =
            entry.credit ||
            (entry.direction === 2 ? entry.amount : entry.credit)
        })
        this.handlSetIndex()
        this.curItemId = this.voucher.entryList[0]?.accountingItemId

        const tableBody = document.getElementById('table-body')
        if (tableBody) {
          tableBody.style.height =
            this.voucher.entryList.length >= 5
              ? '300px'
              : this.voucher.entryList.length * 60 + 'px'
        }
        this.currentEntry = this.voucher.entryList[0]
        this.refreshSubjectBalInRow()
        this.voucherAddInit()
      })
    },
    generateVoucherByRowId() {
      // 根据凭证id渲染页面
      this.loading = true

      this.no_pager = this.noPager
      const params = {
        booksId: this.booksId,
        period: this.period,
        id: this.generateIds,
        mergeItem: this.mergeItem
      }
      generateVoucher(params, this.generateModule)
        .then(({ data }) => {
          this.voucher = data
          this.voucher.entryList.forEach((entry) => {
            entry.assistArr = []
            // entry.businessId = this.generateIds
            entry.debit =
              entry.debit ||
              (entry.direction === 1 ? entry.amount : entry.debit)
            entry.credit =
              entry.credit ||
              (entry.direction === 0 ? entry.amount : entry.credit)
          })
          this.handlSetIndex()
          this.curItemId = this.voucher.entryList[0]?.accountingItemId

          const tableBody = document.getElementById('table-body')
          if (tableBody) {
            tableBody.style.height =
              this.voucher.entryList.length >= 5
                ? '300px'
                : this.voucher.entryList.length * 60 + 'px'
          }
          this.currentEntry = this.voucher.entryList[0]
          this.refreshSubjectBalInRow()
          this.voucherAddInit()
        })
        .finally(() => {
          this.loading = false
        })
    },
    loadDataById() {
      // 根据凭证id渲染页面
      this.loading = true

      this.no_pager = this.noPager
      loadVoucher({
        id: this.id,
        booksId: this.booksId,
        noPager: this.no_pager
      })
        .then((res) => {
          this.voucher = res.data
          this.editStatus = JSON.stringify(this.voucher.entryList)
          if (this.loadType === 'edit') {
            this.voucher.checkerName || this.voucher.bookkeeperName
              ? (this.loadTypeLocal = 'view')
              : (this.loadTypeLocal = 'edit')
          }
          this.voucher.entryList.forEach((entry) => {
            entry.assistArr = []
          })
          this.handlSetIndex()
          this.curItemId = this.voucher.entryList[0]?.accountingItemId
          const tableBody = document.getElementById('table-body')
          if (tableBody) {
            tableBody.style.height =
              this.voucher.entryList.length >= 5
                ? '300px'
                : this.voucher.entryList.length * 60 + 'px'
          }
          this.currentEntry = this.voucher.entryList[0]
          if (this.loadType === 'edit') {
            this.voucherEditInit()
            this.refreshSubjectBalInRow()
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    showBal(item) {
      return (
        this.showSubBalInRow == '1' &&
        this.subjectInputFocus &&
        item.accountingItemId &&
        !item.assistVisible
      )
    },
    refreshSubjectBalInRow() {
      // 刷新行内科目余额
      const tempMap = new Map()
      this.voucher.entryList.forEach((entry, entryIndex) => {
        const accountingItemId = entry.accountingItemId // 科目id
        if (accountingItemId) {
          const currentAccountItem = this.subjectList.find(
            (item) => item.id === entry.accountingItemId
          ) // 科目
          const accountingItemDirection = currentAccountItem.direction // 科目方向
          const debit = entry.debit || 0 // 借方
          const credit = entry.credit || 0 // 贷方
          let endBal = 0
          if (this.subBalInRowMap.has(accountingItemId)) {
            // 先从初始化行内科目余额map中获取
            endBal = this.subBalInRowMap.get(accountingItemId)
          }
          if (tempMap.has(accountingItemId)) {
            // 如果临时map已有该科目数据，覆盖
            endBal = tempMap.get(accountingItemId)
          }
          // 根据科目方向计算余额
          if (debit) {
            endBal =
              endBal +
              (accountingItemDirection == 1 ? Number(debit) : -Number(debit))
          } else if (credit) {
            endBal =
              endBal +
              (accountingItemDirection == 0 ? Number(credit) : -Number(credit))
          }
          // 记录到临时map
          tempMap.set(accountingItemId, endBal)
          this.$set(
            this.voucher.entryList[entryIndex],
            'subjectBalanceInRow',
            comma(endBal, 2)
          )
        } else {
          this.$set(
            this.voucher.entryList[entryIndex],
            'subjectBalanceInRow',
            ''
          )
        }
      })
    },
    goPage(id) {
      if (!id) return
      this.id = id
      this.loadDataById()
    },
    handleClose(add) {
      if (!add) {
        this.$parent[this.dialogName] = false
      }
      this.$emit('close', add)
    },
    handleAdd(key) {
      // 新增行
      const emptyData = {
        summary: '',
        subject: {},
        debit: null,
        credit: null,
        assistArr: []
      }
      this.voucher.entryList.splice(key + 1, 0, emptyData)
      this.handlSetIndex()
      this.$nextTick(() => {
        const tableBody = document.getElementById('table-body')
        if (tableBody) {
          tableBody.style.height =
            this.voucher.entryList.length >= 5
              ? '300px'
              : this.voucher.entryList.length * 60 + 'px'
        }
      })
    },
    handlSetIndex() {
      this.voucher.entryList.forEach((item, index) => {
        item.index = index + 1
      })
    },
    handleRemove(key, item) {
      const addType = this.loadType === 'add' && this.originId
      // 删除行(复制凭证时，不做该校验)
      if (!addType && item.source) {
        return this.$message.warning(this.$t('已关联业务的数据不允许删除'))
      }

      this.voucher.entryList.splice(key, 1)
      if (this.voucher.entryList.length < 2) {
        this.voucher.entryList.push({
          summary: '',
          subject: {},
          debit: null,
          credit: null,
          assistArr: []
        })
      }
      this.handlSetIndex()
      const hasValRow = this.voucher.entryList.find(
        (cur) => cur.accountingItemId
      )
      this.curItemId = hasValRow ? hasValRow.accountingItemId : ''

      this.$nextTick(() => {
        const tableBody = document.getElementById('table-body')
        if (tableBody) {
          tableBody.style.height =
            this.voucher.entryList.length >= 5
              ? '300px'
              : this.voucher.entryList.length * 60 + 'px'
        }
      })
    },
    handleSummaryClick(item, summary) {
      // 选择摘要
      const currentIndex = this.voucher.entryList.indexOf(item)
      this.$set(this.voucher.entryList[currentIndex], 'summary', summary.name)
      this.$nextTick(() => {
        this.$refs['_summaryRef'][0].doClose()
      })
    },
    handleShowDebitInput(itemKey) {
      const addType = this.loadType === 'add' && this.originId
      if (
        this.loadType === 'view' ||
        this.loadType === 'writeOff' ||
        (this.voucher.entryList[itemKey].source && !addType)
      ) {
        return
      }
      this.$set(this.voucher.entryList[itemKey], 'debitShow', true)
      this.$nextTick(() => {
        this.$refs['_inputItem' + (itemKey * 4 + 2)][0].focus()
        this.refreshSubjectBalInRow()
      })
    },
    handleShowCreditInput(itemKey) {
      const addType = this.loadType === 'add' && this.originId
      if (
        this.loadType === 'view' ||
        this.loadType === 'writeOff' ||
        (this.voucher.entryList[itemKey].source && !addType)
      ) {
        return
      }
      this.$set(this.voucher.entryList[itemKey], 'creditShow', true)
      this.$nextTick(() => {
        this.$refs['_inputItem' + (itemKey * 4 + 3)][0].focus()
        this.refreshSubjectBalInRow()
      })
    },
    handleBlur(e, item, show) {
      if (show === 'debitShow') {
        if (isNaN(Number(item.debit))) {
          item.debit = 0
        } else {
          if (Number(item.debit) > 999999999.99) {
            item.debit = 999999999.99
          } else if (Number(item.debit) < -999999999.99) {
            item.debit = -999999999.99
          }
          item.debit = Number(Number(item.debit).toFixed(2))
          if (item.debit) {
            item.credit = 0
          }
        }
      } else if (show === 'creditShow') {
        if (isNaN(Number(item.credit))) {
          item.credit = 0
        } else {
          if (Number(item.credit) > 999999999.99) {
            item.credit = 999999999.99
          } else if (Number(item.credit) < -999999999.99) {
            item.credit = -999999999.99
          }
          item.credit = Number(Number(item.credit).toFixed(2))
          if (item.credit) {
            item.debit = 0
          }
        }
      }
      item[show] = false
    },
    handleMaintainSummary() {
      this.summaryVisible = true
      this.loadSummaryList()
    },
    loadSummaryList() {
      this.summaryLoading = true
      this.summaryQueryParams.booksId = this.booksId
      loadSummaryList(this.summaryQueryParams)
        .then((res) => {
          this.summaryTable.dataList = res.data.rows
          this.summaryTable.total = res.data.total
        })
        .finally(() => {
          this.summaryLoading = false
        })
    },
    handleAddSummary() {
      this.summaryInfoTitle = '新增常用摘要'
      this.isSummaryInfoView = false
      this.summaryInfoVisible = true
      this.summaryInfo = {}
    },
    handleViewSummary(row) {
      this.summaryInfoTitle = '查看常用摘要'
      this.isSummaryInfoView = true
      this.summaryInfoVisible = true
      this.summaryInfoLoading = true
      loadSummaryInfo(row.id)
        .then((res) => {
          this.summaryInfo = res
        })
        .finally(() => {
          this.summaryInfoLoading = false
        })
    },
    handleEditSummary(row) {
      this.summaryInfoTitle = '编辑常用摘要'
      this.isSummaryInfoView = false
      this.summaryInfoVisible = true
      this.summaryInfoLoading = true
      loadSummaryInfo(row.id)
        .then((res) => {
          this.summaryInfo = res
        })
        .finally(() => {
          this.summaryInfoLoading = false
        })
    },
    //   保存摘要
    async handleSaveSummary() {
      const arr = []
      //   去重
      const arrDeduplication = []
      this.voucher.entryList.forEach((item) => {
        if (item?.summary && !arrDeduplication.includes(item.summary)) {
          const obj = {
            booksId: this.booksId,
            summary: item.summary
          }
          arrDeduplication.push(item.summary)
          arr.push(obj)
        }
      })
      if (arr.length <= 0) {
        return this.$message.warning(this.$t('请先录入摘要信息'))
      }
      await batchSave({ booksId: this.booksId }, arr).then((res) => {
        if (res?.returnCode == '0') {
          return this.$message.success(this.$t('保存摘要成功'))
        }
      })
    },
    handleSaveSummaryInfo() {
      this.$refs['_summaryRef'].validate((valid) => {
        if (!valid) return
        const params = {
          id: this.summaryInfo.id || '',
          booksId: this.booksId,
          code: this.summaryInfo.code,
          summary: this.summaryInfo.summary
        }
        saveSummaryInfo(params).then((res) => {
          this.$message.success(res.message)
          if (res.returnCode === '0') {
            this.summaryInfoVisible = false
            this.loadSummaryList()
          }
        })
      })
    },
    handleBatchRemoveSummary() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (selectedRows.length <= 0) {
        return this.$message.warning(this.$t('请先选择要删除的数据'))
      }
      this.handleRemoveSummary(selectedRows, true)
    },
    handleRemoveSummary(row, flag) {
      let params = null

      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          if (flag) {
            // 批量删除
            params = {
              id: row
                .reduce((sum, value) => {
                  return sum + value.id + ','
                }, '')
                .slice(0, -1),
              booksId: this.booksId
            }
            removeBatchRemoveSummaryInfo(params).then((res) => {
              this.$message.success(res.message)
              this.loadSummaryList()
            })
          } else {
            params = { id: row.id }
            removeSummaryInfo(params).then((res) => {
              this.$message.success(res.message)
              this.loadSummaryList()
            })
          }
        })
        .catch(() => {})
    },
    handleSummaryFocus(e, item, key) {
      // 填充摘要
      const currentIndex = this.voucher.entryList.indexOf(item)
      /**
       * 填充条件:(默认填充上一条数据的摘要)
       * 第一条分录或当前分录摘要有数据时不填充
       * 上一条分录有摘要
       */
      if (
        currentIndex !== 0 &&
        !this.voucher.entryList[currentIndex].summary &&
        this.voucher.entryList[currentIndex - 1] &&
        this.voucher.entryList[currentIndex - 1].summary
      ) {
        this.$set(
          this.voucher.entryList[currentIndex],
          'summary',
          this.voucher.entryList[currentIndex - 1].summary
        )
      }
      this.$nextTick(() => {
        e.target.select()
      })
    },
    handleDebitInput(e, item) {
      // 处理借方金额输入
      e = e.replace(/[^0-9.-]/g, '')
      if (!item.credit) {
        item.debit = e
      }
    },
    handleCreditInput(e, item) {
      // 处理贷方金额输入
      e = e.replace(/[^0-9.-]/g, '')
      if (!item.debit) {
        item.credit = e
      }
    },
    handleDebitFocus(e, item) {
      // 填充借方金额
      const currentIndex = this.voucher.entryList.indexOf(item)
      /**
       * 填充条件:(贷方金额总数 - 借方金额总数)
       * 当前分录借方金额无数据
       * 当前分录贷方金额无数据
       * 贷方金额总数 > 借方金额总数
       *
       */
      if (
        !this.voucher.entryList[currentIndex].debit &&
        !this.voucher.entryList[currentIndex].credit &&
        this.creditTotalAmount > this.debitTotalAmount
      ) {
        this.$set(
          this.voucher.entryList[currentIndex],
          'debit',
          this.accSub(this.creditTotalAmount, this.debitTotalAmount)
        )
      }
      this.$nextTick(() => {
        e.target.select()
      })
    },
    handleCreditFocus(e, item) {
      // 填充借方金额
      const currentIndex = this.voucher.entryList.indexOf(item)
      /**
       * 填充条件:(借方金额总数 - 贷方金额总数)
       * 当前分录借方金额无数据
       * 当前分录贷方金额无数据
       * 借方金额总数 > 贷方金额总数
       *
       */
      if (
        !this.voucher.entryList[currentIndex].debit &&
        !this.voucher.entryList[currentIndex].credit &&
        this.debitTotalAmount > this.creditTotalAmount
      ) {
        this.$set(
          this.voucher.entryList[currentIndex],
          'credit',
          this.accSub(this.debitTotalAmount, this.creditTotalAmount)
        )
      }
      this.$nextTick(() => {
        e.target.select()
      })
    },
    handleEntryClick(item) {
      this.currentEntry = item
    },
    handleSummaryDialogClose() {
      getModelSummaryList({ booksId: this.booksId }).then((res) => {
        this.summaryList = res.data
      })
    },
    handleImportVoucher() {
      this.importVisible = true
      this.loadCommonVoucherList()
    },
    loadCommonVoucherList() {
      this.commonVoucherQueryParams.booksId = this.booksId
      this.commonVoucherQueryParams.period = this.period
      this.commonVoucherLoading = true
      mAccountingVoucher(this.commonVoucherQueryParams)
        .then((res) => {
          this.commonVoucherTable.dataList = res.data.rows
          this.commonVoucherTable.total = res.data.total
        })
        .finally(() => {
          this.commonVoucherLoading = false
        })
    },
    handleChooseCommonVoucher() {
      this.selectedCommonVoucher =
        this.$refs['_commonVoucherRef'].$refs['elTable'].selection
      if (this.selectedCommonVoucher.length === 0) {
        return this.$message.warning(this.$t('请选择常用凭证信息'))
      }
      if (this.selectedCommonVoucher.length > 1) {
        return this.$message.warning(
          this.$t('只能选择一条常用凭证信息进行导入')
        )
      }
      this.replaceDate = this.createYear + '-' + this.period
      this.confirmVisible = true
    },
    handleConfirmImportVoucher() {
      getMAccountingVoucher({ id: this.selectedCommonVoucher[0].id }).then(
        (res) => {
          // 判断是否智能替换
          // 凭证信息回填(分录、附单据)
          this.confirmVisible = false
          const commonVoucher = res.data
          commonVoucher.entryList.forEach((ele) => {
            (ele.assistArr = []), (ele.id = '')
          })
          this.voucher.receiptSheets = commonVoucher.receiptSheets
          this.voucher.entryList = commonVoucher.entryList
          this.handlSetIndex()
          if (this.replaceFlag) {
            const arr = this.replaceDate.split('-')
            const year = arr[0]
            const month = arr[1]
            const pYear = /([0-9]{4})年/
            const pMonth = /([0-9]{1,2})月/
            for (let i = 0; i < this.voucher.entryList.length; i++) {
              const entry = this.voucher.entryList[i]
              entry.summary = entry.summary
                .replace(pYear, year + '年')
                .replace(pMonth, month + '月')
              entry.assistArr = []
            }
          }
          if (!this.voucher.entryList.length) {
            this.voucher.entryList = [
              { credit: 0, debit: 0, assistArr: [] },
              { credit: 0, debit: 0, assistArr: [] },
              { credit: 0, debit: 0, assistArr: [] },
              { credit: 0, debit: 0, assistArr: [] },
              { credit: 0, debit: 0, assistArr: [] }
            ]
            this.handlSetIndex()
            const tableBody = document.getElementById('table-body')
            tableBody.style.height = '300px'
          } else {
            const tableBody = document.getElementById('table-body')
            tableBody.style.height = this.voucher.entryList.length * 60 + 'px'
          }
          this.importVisible = false
        }
      )
    },

    handleViewHelp() {
      // const help = this.$router.resolve({ path: '/financial/accounting/help' })
      // window.open(help.href, '_self')
      this.voucherHelpVisible = true
    },

    handleViewCommonVoucher(row) {
      this.commonVoucherInfoTitle = '查看常用会计凭证'
      this.commonVoucherLoadType = 'view'
      loadMAccountingVoucher(row.id).then((res) => {
        this.commonVoucherInfoVisible = true
        this.currentCommonVoucherInfo = res
      })
    },
    handleEditCommonVoucher(row) {
      this.commonVoucherInfoTitle = '编辑常用会计凭证'
      this.commonVoucherLoadType = 'edit'
      loadMAccountingVoucher(row.id).then((res) => {
        this.commonVoucherInfoVisible = true
        this.currentCommonVoucherInfo = res
      })
    },
    handleBatchEditCommonVoucher() {
      this.selectedCommonVoucher =
        this.$refs['_commonVoucherRef'].$refs['elTable'].selection
      if (this.selectedCommonVoucher.length !== 1) {
        return this.$message.warning(this.$t('请选择一个要修改的行'))
      }
      this.handleEditCommonVoucher(this.selectedCommonVoucher[0])
    },
    handleSaveCommonVoucher() {
      this.$refs['_commonVoucherInfoRef'].validate((valid) => {
        if (!valid) return
        const params = {
          booksId: this.booksId
        }
        if (this.commonVoucherLoadType === 'add') {
          // 新增保存
          const data = this.formateParams()
          if (!data) return
          data.sortNumber = this.currentCommonVoucherInfo.sortNumber
          data.voucherName = this.currentCommonVoucherInfo.voucherName
          data.id = ''
          saveMAccountingVoucher(params, data).then((res) => {
            this.$message.success(res.message)
            this.commonVoucherInfoVisible = false
            this.loadCommonVoucherList()
          })
        } else {
          // 编辑保存
          const data = {
            id: this.currentCommonVoucherInfo.id,
            sortNumber: this.currentCommonVoucherInfo.sortNumber,
            voucherName: this.currentCommonVoucherInfo.voucherName
          }
          saveMAccountingVoucher(params, data).then((res) => {
            this.$message.success(res.message)
            this.commonVoucherInfoVisible = false
            this.loadCommonVoucherList()
          })
        }
        this.currentCommonVoucherInfo = {}
      })
    },
    handleAddCommonVoucher() {
      // 凭证存档
      if (!this.formateParams()) return
      this.commonVoucherInfoTitle = '新增常用会计凭证'
      this.commonVoucherLoadType = 'add'
      this.currentCommonVoucherInfo = {}
      this.commonVoucherInfoVisible = true
    },
    handleBatchRemoveCommonVoucher() {
      this.selectedCommonVoucher =
        this.$refs['_commonVoucherRef'].$refs['elTable'].selection
      if (this.selectedCommonVoucher.length <= 0) {
        return this.$message.warning(this.$t('请选择要删除的行'))
      }
      this.handleRemoveCommonVoucher(this.selectedCommonVoucher, true)
    },
    handleRemoveCommonVoucher(row, flag) {
      const params = {
        id: '',
        booksId: this.booksId
      }
      if (flag) {
        // 批量删除
        params.id = row
          .reduce((sum, value) => {
            return sum + value.id + ','
          }, '')
          .slice(0, -1)
      } else {
        params.id = row.id
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          deleteMAccountingVoucher(params).then((res) => {
            this.$message.success(res.message)
            this.loadCommonVoucherList()
          })
        })
        .catch(() => {})
    },

    handleSaveVoucher(add, isClosed, isUpdate, isTemp) {
      const params = {
        booksId: this.booksId,
        period: this.voucher.period || this.period
      }
      const data = this.formateParams(isTemp)
      if (!data) return
      const confirmText = isTemp === 0 ? '保存' : '暂存'
      if (this.loadType === 'add' && this.originId) {
        if (!this.voucher.receiptSheets) {
          this.$confirm(
            this.$t(
              `附单据未填写，确定${isClosed ? '保存并关闭' : confirmText}吗?`
            ),
            {
              confirmButtonText: this.$t('确定'),
              cancelButtonText: this.$t('取消'),
              type: 'warning'
            }
          )
            .then(() => {
              this.isSave = false
              voucherCopy(params, data).then((res) => {
                if (res.returnCode == '0') {
                  this.isSave = true
                  const month = res.data.accountingPeriodIndex || this.period
                  this.$emit('update:originId', '')
                  // this.$emit('update:voucherCopy', false)
                  this.$nextTick(() => {
                    this.$message({
                      type: 'success',
                      message: `已成功新增会计凭证到${this.createYear}年份${month}期间`
                    })
                  })
                  if (isUpdate) {
                    this.initBaseConfigData()
                    return
                  }
                  let isFixedAsset = false
                  this.voucher.entryList.forEach((item) => {
                    const accountingItem = this.subjectList.find(
                      (subject) => subject.id === item.accountingItemId
                    )
                    if (
                      accountingItem &&
                      accountingItem.code.startsWith('151')
                    ) {
                      isFixedAsset = true
                    }
                  })
                  if (this.loadType === 'add' && this.voucher.entryList) {
                    this.$emit('generateFixedAsset', isFixedAsset)
                  }
                  if (isClosed) {
                    this.handleClose(add)
                    this.$emit('success', this.voucher)
                  }
                }
              })
            })
            .catch(() => {})
        } else {
          this.isSave = false
          voucherCopy(params, data).then((res) => {
            if (res.returnCode == '0') {
              this.isSave = true
              const month = res.data.accountingPeriodIndex || this.period
              this.$emit('update:originId', '')
              this.$nextTick(() => {
                this.$message({
                  type: 'success',
                  message: `已成功新增会计凭证到${this.createYear}年份${month}期间`
                })
              })
              if (isUpdate) {
                this.initBaseConfigData()
                return
              }
              if (isClosed) {
                this.handleClose(add)
                this.$emit('success', this.voucher)
              }
            }
          })
        }
      } else {
        // 新增
        if (!this.voucher.receiptSheets) {
          this.$confirm(
            this.$t(
              `附单据未填写，确定${isClosed ? '保存并关闭' : confirmText}吗?`
            ),
            {
              confirmButtonText: this.$t('确定'),
              cancelButtonText: this.$t('取消'),
              type: 'warning'
            }
          )
            .then(() => {
              this.isSave = false
              saveVoucher(params, data).then((res) => {
                if (res.returnCode == '0') {
                  this.isSave = true
                  this.$message.success(res.message)
                  if (isUpdate) {
                    this.initBaseConfigData()
                    return
                  }
                  let isFixedAsset = false
                  this.voucher.entryList.forEach((item) => {
                    const accountingItem = this.subjectList.find(
                      (subject) => subject.id === item.accountingItemId
                    )
                    if (
                      accountingItem &&
                      accountingItem.code.startsWith('151')
                    ) {
                      isFixedAsset = true
                    }
                  })
                  if (this.loadType === 'add' && this.voucher.entryList) {
                    this.$emit('generateFixedAsset', isFixedAsset)
                  }
                  if (isClosed) {
                    this.handleClose(add)
                    this.$emit('success', this.voucher)
                  }
                }
              })
            })
            .catch(() => {})
        } else {
          this.isSave = false
          saveVoucher(params, data).then((res) => {
            if (res.returnCode == '0') {
              this.isSave = true
              this.$message.success(res.message)
              if (isUpdate) {
                this.initBaseConfigData()
                return
              }
              if (isClosed) {
                this.handleClose(add)
                this.$emit('success', this.voucher)
              }
            }
          })
        }
      }
    },
    // eslint-disable-next-line complexity
    formateParams(tempData) {
      // 拼接参数
      const entryList = []
      // 剔除空的分录数据
      const validEntryList = this.voucher.entryList.filter((entry) => {
        return (
          entry.summary || entry.accountingItemId || entry.credit || entry.debit
        )
      })
      if (validEntryList.length < 2) {
        this.$message.warning(this.$t('最少要有两条分录，请检查!'))
        return false
      }
      if (!this.voucher.businessDate) {
        this.$message.warning(this.$t('凭证日期不能为空!'))
        return false
      }
      if (this.debitTotalAmount !== this.creditTotalAmount && tempData !== 1) {
        this.$message.warning(this.$t('试算不平衡!'))
        return false
      }
      if (!this.entryListCheck()) {
        return false
      }
      const data = {
        tempData: tempData,
        accountingPeriodId: this.voucher.accountingPeriodId, // 期间id
        bookkeeperName: this.voucher.bookkeeperName || '', // 记账
        booksId: this.voucher.booksId, // 账套id
        businessDate: this.voucher.businessDate, // 凭证日期
        businessId: this.voucher.businessId || '', // 业务id
        checkerName: this.voucher.checkerName || '', // 审核
        creatorName: this.voucher.creatorName || '', // 制单人
        frequent: this.voucher.frequent || '', // 是否结转、自定义结转凭证，默认为0
        haveWriteOff: this.voucher.haveWriteOff || '', // 冲销凭证数
        id: this.voucher.id || '',
        receiptSheets: this.voucher.receiptSheets, // 附单据
        source: this.voucher.source || '0', // 凭证来源，默认为0
        voucherName: this.voucher.voucherName, // 凭证字
        voucherNumber: this.voucher.voucherNumber, // 凭证号
        writeOffVoucherId: this.voucher.writeOffVoucherId // 冲销凭证数
      }
      // eslint-disable-next-line complexity
      validEntryList.forEach((entry, entryIndex) => {
        entryList.push({
          id: entry.id || '',
          accountingItemId: entry.accountingItemId,
          accountingItemShowName: entry.assistName
            ? entry.accountingItemShowName + entry.assistName
            : entry.accountingItemShowName,
          amount: entry.credit || entry.debit,
          assistName: entry.assistName || '',
          assistProjectId: entry.assistProjectId || '',
          assistTypeId: entry.assistTypeId || '',
          direction: entry.debit ? 1 : 2,
          entryOrderNo: entryIndex + 1,
          settlementDate: entry.settlementDate || '',
          settlementNum: entry.settlementNum || '',
          settlementTypeId: entry.settlementTypeId || '',
          source: entry.source || '',
          businessId: entry.businessId || '', // 业务id
          summary: entry.summary,
          debit: entry.debit,
          credit: entry.credit
        })
      })
      data.entryList = entryList
      return data
    },

    amountToString(amount) {
      // 金额转换为字符串，3.1转换为'310',负数转正
      if (isNaN(Number(amount)) || !amount) {
        return ''
      }
      let s = Math.abs(amount).toString()
      const rs = s.indexOf('.')
      if (rs < 0) {
        s += '.'
      }
      for (let i = s.length - s.indexOf('.'); i <= 2; i++) {
        s += '0'
      }
      s = s.replace(/\./g, '')

      return s
    },
    // eslint-disable-next-line complexity
    entryListCheck() {
      // 检查分录数据,并清除空数据
      let pass = true
      for (let i = 0; i < this.voucher.entryList.length; i++) {
        const entry = this.voucher.entryList[i]
        if (
          !entry.summary &&
          !entry.accountingItemId &&
          !entry.credit &&
          !entry.debit
        ) {
          // 空的分录数据
          continue
        }
        if (
          !(
            entry.summary &&
            entry.accountingItemId &&
            (entry.credit || entry.debit)
          )
        ) {
          this.$message.warning(this.$t('第' + (i + 1) + '行分录没填写完整'))
          pass = false
          return false
        }
        if (
          entry.assistFlag &&
          entry.assistTypeId.split(',').length !== entry.assistFlag &&
          entry.assistProjectId.split(',').length !== entry.assistFlag
        ) {
          this.$message.warning(
            this.$t('第' + (i + 1) + '行分录辅助核算没选择')
          )
          pass = false
          return false
        }
        if (entry.settlementNum && entry.settlementNum.length > 500) {
          this.$message.warning(
            this.$t('第' + (i + 1) + '行分录结算号长度不能大于500')
          )
          pass = false
          return false
        }
      }
      return pass
    },
    /**
     * 是否包含某业务
     * @param source 来源
     * @param businessCode 业务代码
     * @returns {boolean} 结果
     */
    containBusiness(source, businessCode) {
      if (!source) {
        return false
      }
      const bStr = source.toString(2)
      if (bStr.length - 1 < businessCode) {
        return false
      }
      return bStr[bStr.length - 1 - businessCode] == '1'
    },
    /**
     ** 加法函数，用来得到精确的加法结果
     ** 说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
     ** 调用：accAdd(arg1,arg2)
     ** 返回值：arg1加上arg2的精确结果
     **/
    accAdd(arg1, arg2) {
      let r1, r2, m, c
      try {
        r1 = arg1.toString().split('.')[1].length
      } catch (e) {
        r1 = 0
      }
      try {
        r2 = arg2.toString().split('.')[1].length
      } catch (e) {
        r2 = 0
      }
      c = Math.abs(r1 - r2)
      m = Math.pow(10, Math.max(r1, r2))
      if (c > 0) {
        const cm = Math.pow(10, c)
        if (r1 > r2) {
          arg1 = Number(arg1.toString().replace('.', ''))
          arg2 = Number(arg2.toString().replace('.', '')) * cm
        } else {
          arg1 = Number(arg1.toString().replace('.', '')) * cm
          arg2 = Number(arg2.toString().replace('.', ''))
        }
      } else {
        arg1 = Number(arg1.toString().replace('.', ''))
        arg2 = Number(arg2.toString().replace('.', ''))
      }
      return (arg1 + arg2) / m
    },
    /**
     ** 减法函数，用来得到精确的减法结果
     ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
     ** 调用：accSub(arg1,arg2)
     ** 返回值：arg1加上arg2的精确结果
     **/
    accSub(arg1, arg2) {
      let r1, r2, m, n
      try {
        r1 = arg1.toString().split('.')[1].length
      } catch (e) {
        r1 = 0
      }
      try {
        r2 = arg2.toString().split('.')[1].length
      } catch (e) {
        r2 = 0
      }
      m = Math.pow(10, Math.max(r1, r2)) // 动态控制精度长度
      n = r1 >= r2 ? r1 : r2
      return ((arg1 * m - arg2 * m) / m).toFixed(n)
    },
    // 新增项目
    handleAccounting(row, item, key) {
      this.accountingForm.assistTypeName = row.name
      this.accountingForm.booksId = row.booksId
      this.accountingForm.assistTypeId = row.id
      this.accountingRow = item
      this.accountingKey = key
      const data = {
        booksId: row.booksId,
        assistTypeId: row.id
      }
      getMaxProjectCode(data)
        .then((res) => {
          this.accountingForm.projectCode = res.data
          this.accountingVisible = true
        })
        .catch((err) => {
          console.log(err)
          this.accountingVisible = true
        })
    },
    handleSubmit() {
      this.$refs['_accountingRef'].validate(async (valid) => {
        if (valid) {
          try {
            const { message } = await save(this.accountingForm)
            // this.getList()
            this.handleShowAssist(
              this.accountingRow.accountingItemId,
              this.accountingRow,
              this.accountingKey
            )
            this.$message.success(message)
            this.accountingVisible = false
          } catch (error) {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input__suffix {
  display: none;
}
::v-deep .el-message {
  z-index: 9999 !important;
}
::v-deep .el-dialog.voucher {
  .table-retract {
    // margin-right: 6px;
  }
  .el-dialog__body {
    width: 1350px;
    padding: 15px 29px 15px 40px;
    font-family: 'Microsoft Yahei';
    font-size: 16px;
    max-height: 80vh !important;
    color: #000;
    .top-btns {
      margin-left: 20px;
      height: 32px;
      width: calc(100% - 20px);
      display: flex;
      justify-content: flex-end;
      .pager {
        line-height: 28px;
        margin: 0 10px;
        letter-spacing: 5px;
        i {
          font-size: 12px;
          &.normal {
            color: #8f8f8f;
          }
          &.active {
            cursor: pointer;
            color: #4684d5;
          }
        }
      }
    }
    .header-info {
      font-size: 12px;
      height: 62px;
      display: flex;
      justify-content: space-between;
      margin-left: 40px;
      width: calc(100% - 40px);
      .el-date-editor {
        .el-input__inner {
          padding: 0 15px;
        }
        .el-input__prefix {
          display: none;
        }
      }
      .left-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        .balance span {
          color: #33b5ff;
        }
      }
      .center-info {
        width: 160px;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .right-info {
        flex: 1;
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
      input {
        width: 100px;
        border-left: none;
        border-right: none;
        border-top: none;
        border-radius: 0;
      }
      .el-input-number,
      .el-input-number input {
        width: 80px;
        padding: 0;
      }
      .el-date-editor,
      .el-date-editor input {
        width: 125px;
      }
    }
    .table {
      width: 100%;
      flex: 1;
      overflow: hidden;
      font-size: 16px;
      font-family: 'Microsoft Yahei';
      margin-top: 5px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      .table-header,
      .table-body-row,
      .table-footer {
        padding-left: 40px;
        display: flex;
        .cell {
          width: 24%;
          height: 60px;
          border-left: 1px solid #7b7b7b;
          border-top: 1px solid #7b7b7b;
          border-bottom: 1px solid #efefef;

          &:last-child {
            border-right: 1px solid #7b7b7b;
          }
        }
      }
      .table-header {
        .cell {
          text-align: center;
          line-height: 60px;
          &:nth-last-child(-n + 2) {
            line-height: 30px;
            background: url(~@/assets/images/voucher/moneythbg.png) bottom left
              no-repeat;
            background-size: 100%;
          }
          .info {
            color: red;
            font-size: 12px;
          }
        }
        .cell:first-of-type {
          width: 4%;
          text-align: center;
          line-height: 60px;
        }
      }
      .table-body::-webkit-scrollbar {
        width: 0;
      }
      .table-body {
        // margin-right: -6px;
        .cell:first-of-type {
          width: 4%;
          text-align: center;
          line-height: 60px;
        }
        // height: 200px;
        // overflow-y: scroll;
        overflow-y: overlay;
        // 取消input的上下箭头
        input::-webkit-inner-spin-button {
          -webkit-appearance: none !important;
        }
        input::-webkit-outer-spin-button {
          -webkit-appearance: none !important;
        }
        input[type='number'] {
          -moz-appearance: textfield;
        }
        // &::-webkit-scrollbar {
        //   width: 0;
        // }
        .table-body-row {
          position: relative;
          .row-operation {
            position: absolute;
            display: none;
            left: 4px;
            cursor: pointer;
            &:hover {
              color: #4bb8f3;
            }
            &.el-icon-circle-plus-outline {
              top: 0;
            }
            &.el-icon-top {
              top: 0;
              margin-left: 18px;
            }
            &.el-icon-bottom {
              top: 20px;
              margin-left: 18px;
            }
            &.el-icon-circle-close {
              top: 20px;
            }
          }
          .cell {
            overflow: hidden;
            & > .cell-mask {
              width: 100%;
              height: 100%;
              letter-spacing: 16px;
              font-size: 17px;
              text-align: right;
              line-height: 60px;
              background: url(~@/assets/images/voucher/moneytdbg.png) top left
                no-repeat;
              background-size: 100% 60px;
            }
            .el-select.cell-data {
              .el-input__suffix {
                display: none;
              }
            }
            .cell-data textarea,
            .cell-data input {
              padding: 0 5px;
              width: 285px;
              height: 60px;
              border: none;
              border-radius: 0;
              resize: none;
            }
            .cell-data.el-input input {
              text-align: right;
              font-size: 20px;
              color: #4bb8f3;
            }
          }
          &:hover {
            .row-operation {
              display: block;
            }
            .cell {
              &:nth-of-type(1) .cell-data textarea,
              &:nth-of-type(2) .cell-data textarea {
                background-color: #efefef;
                &:focus {
                  background-color: #fff;
                }
              }
              &:nth-of-type(3) .cell-mask,
              &:nth-of-type(4) .cell-mask {
                background: url(~@/assets/images/voucher/moneytdbgon.png) top
                  left no-repeat;
                background-size: 100% 60px;
              }
              &.subject-cell {
                // border-bottom-color: #efefef!important;
                background-color: #efefef;
                textarea {
                }
              }
            }
          }
          .subject-cell {
            position: relative;
            .row-balance {
              color: #606266;
              position: absolute;
              bottom: 0;
              pointer-events: none;
              margin-left: 5px;
              font-size: 12px;
            }
            textarea {
              border-bottom: 22px solid transparent !important;
            }
            .full-cell {
              textarea {
                border-bottom: 1px solid transparent !important;
              }
            }
          }
        }
      }
      .table-footer {
        .cell {
          height: 45px;
          line-height: 45px;
          border-bottom: 1px solid #7b7b7b;
          text-align: right;
          &:nth-of-type(2) {
            padding-right: 10px;
          }
          &:nth-of-type(n + 3) {
            overflow: hidden;
            font-size: 16px;
            letter-spacing: 17px;
            font-weight: bold;
            background: url(~@/assets/images/voucher/moneytdbg.png) top left
              no-repeat;
            background-size: 100% 45px;
          }
          .data {
            line-height: 45px;
            margin-right: -10px;
          }
        }
        .cell:first-of-type {
          width: 28%;
        }
      }
    }
    .footer-info {
      box-sizing: border-box;
      height: 54px;
      margin-left: 40px;
      width: calc(100% - 40px);
      display: flex;
      justify-content: space-between;
      input {
        padding-left: 2px;
        width: 100px;
        border: none;
        border-radius: 0;
        border-bottom: 1px solid #c0c4cc;
      }
      .el-date-editor {
        .el-input__inner {
          padding: 0 15px;
        }
        .el-input__prefix {
          display: none;
        }
      }
    }
  }
  .el-input.is-disabled .el-input__inner {
    background-color: #fff !important;
  }
  .red {
    color: red;
  }
}
#assist-mask {
  position: fixed;
  display: block;
  z-index: 4000;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
::v-deep .current-common-voucher-info {
  .el-input-number {
    input {
      text-align: right;
    }
  }
}

// ::-webkit-scrollbar {
//   width: 0 !important;
// }
// ::-webkit-scrollbar {
//   width: 0 !important;
//   height: 0;
// }

::v-deep .el-dialog.voucher .el-dialog__body .footer-info input {
  min-width: 170px;
}
</style>

<style lang="scss">
.vxe-pulldown--panel {
  z-index: 9999 !important;
}
.box-container {
  max-height: 70vh !important;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.voucher-popper {
  // width: 296px;
  min-height: 0px;
  margin: 0 !important;
  padding: 0;
  font-size: 12px;
  border-radius: 0;
  // border: 1px solid #7b7b7b;
  // box-shadow: 0px 2px 2px rgb(0 0 0 / 30%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .popper__arrow {
    display: none;
  }
  .subject-list {
    width: fit-content;
  }
  .li-item {
    height: 30px;
    line-height: 30px;
    padding-left: 8px;
    cursor: pointer;
    width: fit-content;
    // overflow-x: hidden;
    // text-overflow: ellipsis;
    white-space: nowrap;
    &:hover {
      background-color: #4bb8f3;
    }
  }
  .my-dropdown2 {
     ::v-deep .el-select-dropdown__item {
        overflow: visible !important;
      }
  }
  .el-button {
    width: 100%;
    padding-left: 8px;
    text-align: left;
    background-color: #f2f5f7;
    border-top: 1px solid #4bb8f3;
  }
  .el-select-dropdown__list {
    padding: 0;
  }
}
.assist-popper {
  margin-top: 60px !important;
  z-index: 6000 !important;
}
.assist-form {
  max-height: 250px;
  overflow: hidden;
  .assist-form-form {
    max-height: 250px;
    overflow: scroll;
  }
  * {
    font-size: 12px;
  }
  z-index: 6000;
  .el-form-item {
    display: flex;
    flex-wrap: nowrap;
    margin: 0;
    input {
      padding-left: 2px;
      width: 170px;
      border: none;
      border-radius: 0;
      border-bottom: 1px solid #c0c4cc;
    }
    .voucher-popper {
      width: 175px !important;
    }
  }
}
.amount-popper {
  margin-bottom: 0 !important;
  min-height: 0;
  .debit {
    background-color: #4bb8f3;
    padding: 5px 3px;
    color: #fff;
    font-size: 20px;
    text-align: right;
    height: 26px;
    line-height: 20px;
  }
}
</style>
