/**
 * 发票注册订购相关接口
 */
import request from '@/utils/request'

/** 列表 */
export function page(data) {
  return request({
    url: '/invoice/invoiceRegistry/page',
    method: 'post',
    data: data
  })
}

/** 订购时初始化数据 */
export function initNewData(orgId) {
  return request({
    url: `/invoice/invoiceRegistry/initNewData/${orgId}`,
    method: 'get'
  })
}

/** 进入菜单时加载数据 */
export function initLoad(orgId) {
  return request({
    url: `/invoice/invoiceRegistry/initLoad/${orgId}`,
    method: 'get'
  })
}

/** 查看 */
export function load(id) {
  return request({
    url: `/invoice/invoiceRegistry/load/${id}`,
    method: 'get'
  })
}

/** 查看订购/续约记录 */
export function loadLogs(id) {
  return request({
    url: `/invoice/invoiceRegistry/loadLogs/${id}`,
    method: 'get'
  })
}

/** 保存 */
export function save(data) {
  return request({
    url: '/invoice/invoiceRegistry/save',
    method: 'post',
    data: data
  })
}

/** 续约 */
export function renew(data) {
  return request({
    url: '/invoice/invoiceRegistry/renew',
    method: 'post',
    data: data
  })
}

/** 删除 */
export function deleteByIds(data) {
  return request({
    url: `/invoice/invoiceRegistry/delete`,
    method: 'post',
    data
  })
}

/** 删除订购/续约记录 */
export function deleteRegistryLog(data) {
  return request({
    url: `/invoice/invoiceRegistry/deleteRegistryLog`,
    method: 'post',
    data
  })
}

/** 红冲 */
export function offset(data) {
  return request({
    url: `/invoice/invoiceRegistry/offset`,
    method: 'post',
    data
  })
}

/** 根据机构id查询注册订购的发票信息 */
export function getRegistryByOrg(orgId) {
  return request({
    url: `/invoice/invoiceRegistry/getRegistryByOrg/${orgId}`,
    method: 'get'
  })
}

export function getSwjLoginKey() {
  return request({
    url: `/invoice/digital/getSwjLoginKey`,
    method: 'get',
    payload: true
  })
}

export function sendSwjYzm(data) {
  return request({
    url: `/invoice/digital/sendSwjYzm`,
    method: 'post',
    data: data
  })
}

export function loginSwj(data) {
  return request({
    url: `/invoice/digital/loginSwj`,
    method: 'post',
    data
  })
}

/** 查询电子税务局登录状态 */
export function getSwjLoginStatus(registryType, spId, taxNo) {
  return request({
    url: `/invoice/digital/getSwjLoginStatus/${registryType}?spId=${spId}&taxNo=${taxNo}`,
    method: 'get'
  })
}

/** 获取电子税务局登录二维码 */
export function getSwjLoginQrCode(registryType, smLx, spId, taxNo) {
  return request({
    url: `/invoice/digital/getSwjLoginQrCode/${registryType}?spId=${spId}&taxNo=${taxNo}&smLx=${smLx}`,
    method: 'get'
  })
}

/** 获取电子税务局扫码认证结果 */
export function getSwjLoginResult(registryType, smLx, spId, taxNo, rzId) {
  return request({
    url: `/invoice/digital/getSwjLoginResult/${registryType}?spId=${spId}&taxNo=${taxNo}&smLx=${smLx}&rzId=${rzId}`,
    method: 'get',
    data
  })
}
