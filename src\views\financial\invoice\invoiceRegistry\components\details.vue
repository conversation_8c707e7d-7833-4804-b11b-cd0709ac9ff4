<template>
  <gever-dialog
    :title="$t(`发票注册订购${title}`)"
    :visible="dialogVisible"
    style="margin-top: 12vh"
    width="980px"
    @close="handleClose"
  >
    <el-form
      ref="_formRef"
      :inline="true"
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="110px"
      class="gever-form"
      :disabled="statusType === 'view' || statusType === 'renew'"
    >
      <div class="form-sg">
        <el-form-item :label="$t('单位名称')" prop="orgName">
          <el-input v-model="form.orgName" type="text" :maxlength="100" />
        </el-form-item>
        <el-form-item :label="$t('终端ID')" prop="spId">
          <el-input v-model="form.spId" type="text" disabled />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('纳税人类型')" prop="taxpayerType">
          <gever-select
            v-model="form.taxpayerType"
            path="/financial/invoice/taxpayerType/"
          />
        </el-form-item>
        <el-form-item :label="$t('纳税人识别号')" prop="taxpayerNo">
          <el-input
            v-model="form.taxpayerNo"
            type="text"
            :maxlength="50"
            :disabled="form.registryFlag === '1'"
          />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('已注册订购')" prop="registryFlag">
          <el-switch
            v-model="form.registryFlag"
            active-value="1"
            inactive-value="0"
            active-color="#13ce66"
            disabled
          />
        </el-form-item>
        <el-form-item :label="$t('发票服务商')" prop="registryType">
          <gever-select
            v-model="form.registryType"
            path="/financial/invoice/registryType/"
            :disabled="form.registryFlag === '1'"
          />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('地址')" prop="contactAddress">
          <el-input
            v-model="form.contactAddress"
            type="text"
            :maxlength="200"
          />
        </el-form-item>
        <el-form-item :label="$t('电话')" prop="contactPhone">
          <el-input v-model="form.contactPhone" type="text" :maxlength="20" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('开户银行')" prop="accountName">
          <el-input v-model="form.accountName" type="text" :maxlength="100" />
        </el-form-item>
        <el-form-item :label="$t('银行账号')" prop="accountNo">
          <el-input v-model="form.accountNo" type="text" :maxlength="50" />
        </el-form-item>
      </div>
      <div v-if="!['view', 'renew'].includes(statusType)" class="form-sg">
        <el-form-item :label="$t('电子税务局账号')" prop="swjAccount">
          <el-input v-model="form.swjAccount" type="text" :maxlength="100" />
        </el-form-item>
        <el-form-item :label="$t('电子税务局密码')" prop="swjPassword">
          <el-input
            v-model="form.swjPassword"
            type="password"
            show-password
            autocomplete="new-password"
            :maxlength="50"
          />
        </el-form-item>
      </div>
      <!-- 南海专用，百望开票的有效期与合同附件 $store.state.user.areaCode.startsWith('D440605') -->
      <div
        v-if="
          areaLogin.areaCode.startsWith('D440605') && form.registryFlag !== '1'
        "
        class="form-sg"
      >
        <el-form-item label="百望合同有效期" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            unlink-panels
          />
        </el-form-item>
        <el-form-item label="百望合同上传">
          <gever-upload
            ref="_bwFilesRef"
            list-type="form-list"
            accept="jpg,jpeg,png,doc,xls,txt,pdf,docx,xlsx,zip,rar"
            :business-id="form.id"
            file-classification="bwInvoice"
            :default-batch-id="defaultBathId"
            @batch-change="(val) => (fileBatchId = val)"
          />
        </el-form-item>
      </div>
      <div v-if="form.registryFlag === '1'" class="form-sg">
        <el-form-item label="有效开始日期" prop="validBeginDate">
          <el-date-picker
            v-model="form.validBeginDate"
            type="date"
            value-format="yyyy-MM-dd"
            disabled
          />
        </el-form-item>
        <el-form-item label="有效结束日期" prop="validEndDate">
          <el-date-picker
            v-model="form.validEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            disabled
          />
        </el-form-item>
      </div>
    </el-form>
    <template v-if="statusType !== 'view'" #footer>
      <el-button @click="handleClose">{{ $t('取 消') }}</el-button>
      <el-button
        v-if="statusType === 'renew'"
        type="primary"
        icon="el-icon-s-operation"
        :loading="loading"
        @click="handleRenew"
      >
        {{ $t('续 约') }}
      </el-button>
      <el-button
        v-if="['add', 'edit'].includes(statusType) && form.registryFlag !== '1'"
        type="primary"
        icon="el-icon-document"
        :loading="loading"
        @click="handleSave(false)"
      >
        {{ $t('保 存') }}
      </el-button>
      <el-button
        v-if="['add', 'edit'].includes(statusType)"
        type="primary"
        icon="el-icon-document-checked"
        :loading="loading"
        @click="handleSave(true)"
      >
        {{ form.registryFlag === '1' ? $t('更新信息') : $t('保存&订购') }}
      </el-button>
    </template>
  </gever-dialog>
</template>

<script>
import {
  save,
  load,
  getSwjLoginKey,
  initNewData,
  renew
} from '@/api/financial/invoice/invoiceRegistry.js'
import { uuid } from '@/utils/gever.js'
import JSEncrypt from 'jsencrypt'

export default {
  name: 'InvoiceRegistryDetails',
  props: {
    dialogVisible: { type: Boolean, default: false },
    formData: {
      type: Object,
      default: () => {}
    },
    statusType: { type: String, default: 'view' },
    areaLogin: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dateRange: [],
      form: {
        orgId: this.areaLogin.id,
        registryFlag: '',
        registryType: '',
        swjAccount: '',
        swjPassword: '',
        validBeginDate: '',
        validEndDate: '',
        sameRegistry: false // 是否同时注册
      },
      rules: {
        orgName: [
          {
            required: true,
            message: '请输入单位名称',
            trigger: ['blur', 'change']
          }
        ],
        taxpayerType: [
          {
            required: true,
            message: '请选择纳税人类型',
            trigger: ['blur', 'change']
          }
        ],
        taxpayerNo: [
          {
            required: true,
            message: '请输入纳税人识别号',
            trigger: ['blur', 'change']
          }
        ],
        registryType: [
          {
            required: true,
            message: '请选择发票服务商',
            trigger: ['blur', 'change']
          }
        ],
        contactAddress: [
          {
            required: true,
            message: '请输入地址',
            trigger: ['blur', 'change']
          }
        ],
        contactPhone: [
          {
            required: true,
            message: '请输入电话',
            trigger: ['blur', 'change']
          }
        ],
        accountName: [
          {
            required: true,
            message: '请输入开户银行',
            trigger: ['blur', 'change']
          }
        ],
        accountNo: [
          {
            required: true,
            message: '请输入银行账号',
            trigger: ['blur', 'change']
          }
        ]
      },
      defaultBathId: '',
      fileBatchId: '',
      taxpayerTypeOptions: [],
      loading: false,
      title: '',
      publicKey: ''
    }
  },
  computed: {},
  watch: {
    statusType: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.defaultBathId = uuid()
        if (newVal === 'view') {
          this.getDetail()
          // this.title = '（查看）'
        } else if (newVal === 'edit') {
          this.getDetail()
          // this.title = '（编辑）'
        } else if (newVal === 'renew') {
          this.getDetail()
          this.title = '（续约）'
        } else {
          // this.title = '（新增）'
          this.initNewData()
        }
      }
    },
    dateRange: {
      deep: true,
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length === 2) {
          this.$set(this.form, 'validBeginDate', newVal[0])
          this.$set(this.form, 'validEndDate', newVal[1])
        } else {
          this.$set(this.form, 'validBeginDate', '')
          this.$set(this.form, 'validEndDate', '')
        }
      }
    }
  },
  mounted() {
    this.fetchPublicKey()
  },
  methods: {
    getDetail() {
      this.loading = true
      load(this.formData.id)
        .then((res) => {
          this.form = {
            ...res
          }
          if (this.form.validBeginDate && this.form.validEndDate) {
            this.dateRange = [this.form.validBeginDate, this.form.validEndDate]
          }
          this.form.registryFlag = this.form.registryFlag + ''
        })
        .finally(() => {
          this.loading = false
        })
    },
    /* 关闭弹窗 */
    handleClose() {
      this.defaultBathId = ''
      this.fileBatchId = ''
      this.$emit('updateList')
      this.$emit('update:dialogVisible', false)
    },
    async fetchPublicKey() {
      try {
        const res = await getSwjLoginKey()
        if (res.returnCode === '0') {
          this.publicKey = res.message
        } else {
          this.$message.error('无法获取公钥:' + res.message)
        }
      } catch (error) {
        console.error('无法获取公钥:', error)
      }
    },
    encryptData(data) {
      const encryptor = new JSEncrypt()
      encryptor.setPublicKey(this.publicKey)
      return encryptor.encrypt(data)
    },
    initNewData() {
      this.loading = true
      initNewData(this.areaLogin.id)
        .then((res) => {
          const registryData = res.data
          this.$set(this.form, 'registryFlag', '0')
          this.$set(this.form, 'registryType', '')
          this.$set(this.form, 'taxpayerType', registryData.taxpayerType || '')
          this.$set(this.form, 'taxpayerNo', registryData.taxpayerNo || '')
          this.$set(this.form, 'accountName', registryData.accountName || '')
          this.$set(this.form, 'accountNo', registryData.accountNo || '')
          this.$set(
            this.form,
            'contactAddress',
            registryData.contactAddress || ''
          )
          this.$set(this.form, 'contactPhone', registryData.contactPhone || '')
          if ((registryData.orgName || '') === '') {
            this.$set(this.form, 'orgName', this.areaLogin.fname)
          } else {
            this.$set(this.form, 'orgName', registryData.orgName)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /* 确认按钮 */
    handleSave(regisFlag) {
      this.$refs['_formRef'].validate((valid) => {
        if (valid) {
          this.form.sameRegistry = !!regisFlag
          const params = {
            ...this.form,
            orgId: this.areaLogin.id
          }
          if (this.form.swjPassword && this.form.swjPassword.trim() !== '') {
            params.swjPassword = this.encryptData(this.form.swjPassword)
          }
          params.fileBatchId = this.fileBatchId || this.defaultBathId
          this.loading = true
          save(params)
            .then((res) => {
              this.$message.success(res.message)
              this.$emit('updateList')
              this.$emit('update:dialogVisible', false)
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    /* 续约 */
    handleRenew() {
      const params = {
        ...this.form,
        orgId: this.areaLogin.id
      }
      this.loading = true
      renew(params)
        .then((res) => {
          this.$message.success(res.message)
          this.$emit('updateList')
          this.$emit('update:dialogVisible', false)
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
