<!--
 * @Descripttion: 数电发票开具
-->
<template>
  <gever-dialog
    :title="$t(`发票开出`)"
    :visible="dialogVisible"
    width="90%"
    :append-to-body="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="dialog-box">
      <el-form
        ref="_formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="right"
        label-width="120px"
        class="gever-form"
        :disabled="statusType === 'view'"
      >
        <div class="form-title">
          <span>{{ $t(invoiceTypeSelect.abbr) }}</span>
          <span
            v-if="statusType === 'red' || rowData.offsetType !== 0"
            class="red"
          >
            {{ $t(rowData.offsetType !== 2 ? '（红冲）' : '（被红冲）') }}
          </span>
        </div>
        <div class="form-sg sh_1">
          <el-form-item
            v-if="statusType === 'view'"
            :label="$t('发票号码：')"
            prop="invoiceNo"
          >
            <el-input v-model="form.invoiceNo" type="text" disabled />
          </el-form-item>
          <el-form-item
            v-if="statusType === 'view'"
            :label="$t('开票日期：')"
            prop="drawDate"
          >
            <gever-date-picker
              v-model="form.drawDate"
              type="date"
              value-format="yyyy-MM-dd"
              disabled
            />
          </el-form-item>
          <el-form-item
            v-if="statusType === 'red' || form.offsetType === 1"
            :label="$t('原发票号码：')"
            prop="offsetSourceNo"
          >
            <el-input v-model="form.offsetSourceNo" type="text" disabled />
          </el-form-item>
          <el-form-item
            v-if="statusType === 'view' && form.offsetType === 2"
            :label="$t('红冲发票号码：')"
            prop="offsetSourceNo"
          >
            <el-input v-model="form.offsetInvoiceNo" type="text" disabled />
          </el-form-item>
        </div>
        <div class="partition-box">
          <div class="partition-left">
            <div class="partition-title">{{ $t('付款方信息	') }}</div>
            <div class="form-sg">
              <el-form-item :label="$t('名称：')" prop="payerName">
                <el-autocomplete
                  v-model.trim="form.payerName"
                  :rows="1"
                  :maxlength="1000"
                  :disabled="statusType === 'red' || statusType === 'view'"
                  :title="form.payerName"
                  :fetch-suggestions="(queryString, callback) => getCashierCommonInfo(queryString, callback)"
                  :placeholder="$t('请输入付款方名称')"
                  style="width: 100%"
                  @select="handlePayerInfo"
                >
                  <template #default="{ item }">
                    <el-row type="flex" justify="space-between" :title="item.payerName">
                      <span class="overflow-hidden">{{ item.payerName }}</span>
                    </el-row>
                  </template>
                </el-autocomplete>
              </el-form-item>
              <el-button
                :disabled="statusType === 'red' || statusType === 'view'"
                style="width: 100px; height: 30px; color: #409eff"
                @click="savePayer"
              >
                {{ $t('保存付款方') }}
              </el-button>
            </div>
            <div class="form-sg">
              <el-form-item
                :label="$t('纳税人识别号：')"
                prop="payerTaxpayerNo"
                :rules="
                  form.invoiceKind == 1
                    ? rules.payerTaxpayerNo
                    : [{ required: false }]
                "
              >
                <gever-input
                  v-model="form.payerTaxpayerNo"
                  :disabled="statusType === 'red'"
                />
              </el-form-item>
            </div>
          </div>
          <div class="partition-right">
            <div class="partition-title">{{ $t('收款方信息	') }}</div>
            <div class="form-sg">
              <el-form-item :label="$t('名称：')" prop="sellerName">
                <el-input
                  v-model="form.sellerName"
                  :disabled="statusType === 'red'"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-sg">
              <el-form-item
                :label="$t('纳税人识别号：')"
                prop="sellerTaxpayerNo"
              >
                <el-input
                  v-model="form.sellerTaxpayerNo"
                  :disabled="statusType === 'red'"
                />
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="table-box">
          <div class="table-head">
            <div class="table-title">{{ $t('开票信息') }}</div>
            <div class="table-btn">
              <el-button
                type="default"
                round
                :disabled="
                  statusType === 'red' ||
                    statusType === 'view' ||
                    table.rows.length >= (invoiceTypeSelect.detailSize || 3)
                "
                @click="handleContractAdd"
              >
                {{ $t('添加') }}
              </el-button>
            </div>
          </div>
          <gever-table
            ref="_geverTableRef"
            :columns="tableColumns"
            :data="table.rows"
            highlight-current-row
            height="170"
            :pagination="false"
            :summary-method="getSummaries"
            show-summary
            :class="statusType === 'red' || rowData.offsetType !== 0 ? 'red-status' : ''"
          >
            <template #incomeType="{ row, index }">
              <el-cascader
                :ref="`_cascaderRowRef${index}`"
                v-model="row.incomeType"
                style="width: 100%"
                :options="handleOptionsCascader(row.incomeType)"
                :disabled="
                  ['red', 'view'].includes(statusType) ||
                    (row.incomeType && row.incomeType.includes('1'))
                "
                :props="{
                  expandTrigger: 'hover',
                  checkStrictly: true,
                  value: 'id',
                  label: 'text'
                }"
              ></el-cascader>
            </template>
            <!-- 税收分类 -->
            <template #taxKindCode="{ row, index }">
              <el-select
                :ref="`selectTree${index}`"
                v-model="row.taxKindCode"
                class="main-select-tree"
                style="width: 100%"
                :disabled="statusType === 'red' || statusType === 'view'"
              >
                <el-option
                  v-for="item in formatData(optionsTaxKindCode)"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                  style="display: none"
                />
                <el-tree
                  ref="selecteltree"
                  class="main-select-el-tree"
                  :data="optionsTaxKindCode"
                  node-key="id"
                  highlight-current
                  :props="defaultProps"
                  :current-node-key="row.taxKindCode"
                  :expand-on-click-node="expandOnClickNode"
                  default-expand-all
                  @node-click="handleTaxKind($event, index)"
                />
              </el-select>
            </template>
            <!-- 收费项目 -->
            <template #chargeItem="{ row, index }">
              <el-autocomplete
                v-model="row.chargeItem"
                :fetch-suggestions="querySearch"
                value-key="itemName"
                style="width: 100%"
                :disabled="statusType === 'red' || statusType === 'view'"
                @blur="handleBlur(row, index)"
                @select="handleBlur(row, index)"
              >
                <template #suffix>
                  <el-tooltip placement="top" :content="row.chargeItem">
                    <i v-show="row.chargeItem" class="el-icon-question"></i>
                  </el-tooltip>
                </template>
                <template slot-scope="{ item }">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="item.itemName"
                    placement="right"
                    :disabled="item.itemName.length <= 8"
                  >
                    <div class="charge-item">{{ item.itemName }}</div>
                  </el-tooltip>
                </template>
              </el-autocomplete>
            </template>
            <!-- 单位 -->
            <template #unit="{ row }">
              <el-input
                v-model="row.unit"
                :disabled="statusType === 'red' || statusType === 'view'"
              ></el-input>
            </template>
            <!-- 数量 -->
            <template #qty="{ row }">
              <el-input
                v-model="row.qty"
                :class="
                  statusType === 'red' || rowData.offsetType !== 0
                    ? 'alingRightRed'
                    : 'alingRight'
                "
                :disabled="statusType === 'red' || statusType === 'view'"
                @blur="qtyBlur"
              ></el-input>
            </template>
            <!-- 单价 -->
            <template #price="{ row }">
              <gever-input
                v-model="row.price"
                class="alingRight"
                :disabled="statusType === 'red' || statusType === 'view'"
                :clearable="false"
                @blur="unitPriceBlur"
              ></gever-input>
            </template>
            <!-- 商品/服务金额 -->
            <template #saleAmount="{ row, index }">
              <el-input
                v-model="row.saleAmount"
                class="alingRight"
                :class="
                  statusType === 'red' || rowData.offsetType !== 0
                    ? 'alingRightRed'
                    : 'alingRight'
                "
                :disabled="
                  ['red', 'view'].includes(statusType) ||
                    (row.incomeType && row.incomeType.includes('1'))
                "
                @input="handleSaleAmountInput($event, index)"
                @blur="inputBlur"
              ></el-input>
            </template>
            <!-- 含税 -->
            <template #includeTax="{ row, index }">
              <el-checkbox
                v-model="row.includeTax"
                :disabled="statusType === 'red' || statusType === 'view'"
                :true-label="1"
                :false-label="0"
                @change="handleIncludeTax($event, index)"
              />
            </template>
            <!-- 税率 -->
            <template #taxRate="{ row, index }">
              <gever-select
                v-model="row.taxRate"
                path="/financial/invoice/taxRate/"
                :disabled="statusType === 'red' || statusType === 'view'"
                number-key
                @change="handleTaxRate($event, index)"
              ></gever-select>
            </template>
            <!-- 税额 -->
            <template #taxAmount="{ row }">
              <span
                :class="
                  statusType === 'red' || rowData.offsetType !== 0 ? 'red' : ''
                "
              >
                {{ comma(row.taxAmount) }}
              </span>
            </template>
            <!-- 应税金额 -->
            <template #taxableAmount="{ row }">
              <span
                :class="
                  statusType === 'red' || rowData.offsetType !== 0 ? 'red' : ''
                "
              >
                {{ comma(row.taxableAmount) }}
              </span>
            </template>
            <!-- 操作 -->
            <template #operation="{ row, index }">
              <el-button
                type="text"
                :disabled="statusType === 'red' || statusType === 'view'"
                @click="handleContract(index)"
              >
                {{ $t('选择合同项目') }}
              </el-button>
              <el-button
                type="text"
                :disabled="statusType === 'red' || statusType === 'view'"
                @click="handleDelete(row, index)"
              >
                {{ $t('删除') }}
              </el-button>
            </template>
          </gever-table>
          <div class="table-footer">
            <div class="footer-box">
              <span>{{ $t('价税合计（大写）：') }}</span>
              <span :class="statusType === 'red' || rowData.offsetType !== 0 ? 'red' : ''">
                {{ form.totalAmount.toString() ? smallToBig(form.totalAmount) : '' }}
              </span>
            </div>
            <div class="footer-box">
              <span>{{ $t('价税合计：') }}</span>
              <span :class="statusType === 'red' || rowData.offsetType !== 0 ? 'red' : ''">
                {{ form.totalAmount.toString() ? comma(form.totalAmount) : '' }}
              </span>
            </div>
          </div>
        </div>
        <div class="form-sg">
          <el-form-item prop="summary" label-width="80px">
            <template slot="label">
              <div style="line-height: 20px; margin-top: 2px; float: right">
                <span style="font-size: 14px">摘要：</span>
                <span
                  v-if="['add', 'edit', 'red', 'supply'].includes(statusType)"
                >
                  <br />
                </span>
                <span
                  v-if="['add', 'edit', 'red', 'supply'].includes(statusType)"
                  class="lockSpan"
                  @click="clickLock($event, 'summary')"
                >
                  锁定
                </span>
              </div>
            </template>
            <gever-input
              v-model="form.summary"
              type="textarea"
              maxlength="200"
              :rows="3"
              :disabled="statusType === 'red' || summaryLock"
            />
          </el-form-item>
          <el-form-item prop="remark" label-width="75px">
            <template slot="label">
              <div style="line-height: 20px; margin-top: 2px">
                <span style="font-size: 14px">备注：</span>
                <span
                  v-if="['add', 'edit', 'red', 'supply'].includes(statusType)"
                >
                  <br />
                </span>
                <span
                  v-if="['add', 'edit', 'red', 'supply'].includes(statusType)"
                  class="lockSpan"
                  @click="clickLock($event, 'remark')"
                >
                  锁定
                </span>
              </div>
            </template>
            <gever-input
              v-model="form.remark"
              type="textarea"
              maxlength="200"
              :rows="3"
              :disabled="statusType === 'red' || remarkLock"
            />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('收款人：')" label-width="80px" prop="payee">
            <el-autocomplete
              v-model="form.payee"
              :fetch-suggestions="getFetchSuggestions"
              value-key="operatorName"
              :disabled="statusType === 'red'"
              @input="handleInputChange1"
            >
              <el-button
                slot="append"
                :disabled="!form.payee"
                @click="handleSelectPrayer('payee')"
              >
                {{ $t('保存') }}
              </el-button>
            </el-autocomplete>
          </el-form-item>
          <el-form-item :label="$t('复核人：')" label-width="75px" prop="reviewer">
            <el-autocomplete
              v-model="form.reviewer"
              :fetch-suggestions="getFetchSuggestions2"
              value-key="operatorName"
              :disabled="statusType === 'red'"
              @input="handleInputChange"
            >
              <el-button
                slot="append"
                :disabled="!form.reviewer"
                @click="handleSelectPrayer('reviewer')"
              >
                {{ $t('保存') }}
              </el-button>
            </el-autocomplete>
          </el-form-item>
          <el-form-item :label="$t('开票人：')" label-width="75px" prop="drawer">
            <el-input v-model="form.drawer" type="text" disabled />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item
            prop="tdyslxDm"
            label-width="80px"
            :label="$t('特定业务：')"
          >
            <gever-select
              v-model="form.tdyslxDm"
              style="width: 150px"
              :options="tdywOptions"
              :disabled="statusType === 'red'"
              @change="handleTdyslxDmChange"
            />
            <el-button
              type="default"
              round
              style="margin-left: 20px"
              :disabled="
                statusType === 'red' ||
                  (form.tdyslxDm || '') === '' ||
                  (form.tdyslxDm === '03' && form.invoiceSpecList.length > 0) ||
                  ['05', '06'].includes(form.tdyslxDm)
              "
              @click="handleAddSpec"
            >
              {{ $t('增加行') }}
            </el-button>
          </el-form-item>
        </div>
        <div v-if="(form.tdyslxDm || '') !== ''" class="table-box">
          <gever-table
            ref="_tdywTableRef"
            highlight-current-row
            height="170"
            :columns="tdywColumns"
            :data="form.invoiceSpecList"
            :pagination="false"
          >
            <!-- 不动产租赁地区(省市区) -->
            <template #bdcSf="{ row }">
              <gever-select
                v-if="row.rowState === true"
                v-model="row.bdcSf"
                :class="['add', 'edit', 'supply'].includes(statusType) ? 'rowSf' : ''"
                :options="dqAreaOptions"
                :disabled="statusType === 'red'"
              />
              <el-input
                v-else
                v-model="row.bdcSf"
                type="text"
                :class="['add', 'edit', 'supply'].includes(statusType) ? 'rowSf' : ''"
                :disabled="statusType === 'red'"
              />
              <span v-if="['add', 'edit', 'supply'].includes(statusType)" class="rowSfSpan" @click="handleRowState(row)">
                {{ row.rowState ? $t('输入') : $t('选择') }}
              </span>
            </template>
            <!-- 不动产租赁详细地址 -->
            <template #bdcXxdz="{ row }">
              <el-input
                v-model="row.bdcXxdz"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产租赁起始日期 -->
            <template #bdcQsrq="{ row }">
              <gever-date-picker
                v-model="row.bdcQsrq"
                type="date"
                value-format="yyyy-MM-dd"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产租赁截止日期 -->
            <template #bdcJzrq="{ row }">
              <gever-date-picker
                v-model="row.bdcJzrq"
                type="date"
                value-format="yyyy-MM-dd"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产租赁跨市标志 -->
            <template #bdcKsbz="{ row }">
              <gever-select
                v-model="row.bdcKsbz"
                number-key
                :clearable="false"
                :options="yesOrNoOptions"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产租赁产权证号 -->
            <template #bdcCqjh="{ row }">
              <el-input
                v-model="row.bdcCqjh"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产租赁面积单位 -->
            <template #bdcPjdw="{ row }">
              <gever-select
                v-model="row.bdcPjdw"
                :options="bdcUnitOptions"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产销售产品证号 -->
            <template #bdcxsCqzh="{ row }">
              <el-input
                v-model="row.bdcxsCqzh"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产销售地区(省市区) -->
            <template #bdcxsDqdz="{ row }">
              <gever-select
                v-if="row.rowState === true"
                v-model="row.bdcxsDqdz"
                :class="['add', 'edit', 'supply'].includes(statusType) ? 'rowSf' : ''"
                :options="dqAreaOptions"
                :disabled="statusType === 'red'"
              />
              <el-input
                v-else
                v-model="row.bdcxsDqdz"
                type="text"
                :class="['add', 'edit', 'supply'].includes(statusType) ? 'rowSf' : ''"
                :disabled="statusType === 'red'"
              />
              <span v-if="['add', 'edit', 'supply'].includes(statusType)" class="rowSfSpan" @click="handleRowState(row)">
                {{ row.rowState ? $t('输入') : $t('选择') }}
              </span>
            </template>
            <!-- 不动产销售详细地址 -->
            <template #bdcxsXxdz="{ row }">
              <el-input
                v-model="row.bdcxsXxdz"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产销售合同编号 -->
            <template #bdcxsHtbh="{ row }">
              <el-input
                v-model="row.bdcxsHtbh"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产销售项目编号 -->
            <template #bdcxsXmbh="{ row }">
              <el-input
                v-model="row.bdcxsXmbh"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产销售计税价格 -->
            <template #bdcxsJsjg="{ row }">
              <el-input-number
                v-model="row.bdcxsJsjg"
                :controls="false"
                :precision="2"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产销售成交价格 -->
            <template #bdcxsCjje="{ row }">
              <el-input-number
                v-model="row.bdcxsCjje"
                :controls="false"
                :precision="2"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产销售跨区标志 -->
            <template #bdcxsKqbz="{ row }">
              <gever-select
                v-model="row.bdcxsKqbz"
                number-key
                :clearable="false"
                :options="yesOrNoOptions"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 不动产销售面积单位 -->
            <template #bdcxsMjdw="{ row }">
              <gever-select
                v-model="row.bdcxsMjdw"
                :options="bdcUnitOptions"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 旅客运输出行人 -->
            <template #lkysCxr="{ row }">
              <el-input
                v-model="row.lkysCxr"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 旅客运输证件类型 -->
            <template #lkysCxrzjlx="{ row }">
              <gever-select
                v-model="row.lkysCxrzjlx"
                :options="idCardTypeOptions"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 旅客运输证件号码 -->
            <template #lkysCxrzjhm="{ row }">
              <el-input
                v-model="row.lkysCxrzjhm"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 旅客运输出行日期 -->
            <template #lkysCxrq="{ row }">
              <gever-date-picker
                v-model="row.lkysCxrq"
                type="date"
                value-format="yyyy-MM-dd"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 旅客运输出发地 -->
            <template #lkysCfd="{ row }">
              <el-input
                v-model="row.lkysCfd"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 旅客运输到达地 -->
            <template #lkysDdd="{ row }">
              <el-input
                v-model="row.lkysDdd"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 旅客运输交通工具类型 -->
            <template #lkysJtgjlx="{ row }">
              <gever-select
                v-model="row.lkysJtgjlx"
                :options="vehicleTypeOptions"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 旅客运输等级 -->
            <template #lkysDj="{ row }">
              <gever-select
                v-if="['1'].includes(row.lkysJtgjlx)"
                v-model="row.lkysDj"
                :options="fjLevelOptions"
                :disabled="statusType === 'red'"
              />
              <gever-select
                v-else-if="['2'].includes(row.lkysJtgjlx)"
                v-model="row.lkysDj"
                :options="hcLevelOptions"
                :disabled="statusType === 'red'"
              />
              <gever-select
                v-else-if="['7'].includes(row.lkysJtgjlx)"
                v-model="row.lkysDj"
                :options="cbLevelOptions"
                :disabled="statusType === 'red'"
              />
              <el-input
                v-else
                v-model="row.lkysDj"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 货物运输起运地 -->
            <template #hwysQyd="{ row }">
              <el-input
                v-model="row.hwysQyd"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 货物运输到达地 -->
            <template #hwysDdd="{ row }">
              <el-input
                v-model="row.hwysDdd"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 货物运输运输工具 -->
            <template #hwysYsgj="{ row }">
              <gever-select
                v-model="row.hwysYsgj"
                :options="wayTypeOptions"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 货物运输运输工具牌号 -->
            <template #hwysYsgjth="{ row }">
              <el-input
                v-model="row.hwysYsgjth"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 货物运输货物名称 -->
            <template #hwysYshwmc="{ row }">
              <el-input
                v-model="row.hwysYshwmc"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 建筑服务发生地(省市区) -->
            <template #gzfwFsd="{ row }">
              <gever-select
                v-if="row.rowState === true"
                v-model="row.gzfwFsd"
                :class="['add', 'edit', 'supply'].includes(statusType) ? 'rowSf' : ''"
                :options="dqAreaOptions"
                :disabled="statusType === 'red'"
              />
              <el-input
                v-else
                v-model="row.gzfwFsd"
                type="text"
                :class="['add', 'edit', 'supply'].includes(statusType) ? 'rowSf' : ''"
                :disabled="statusType === 'red'"
              />
              <span v-if="['add', 'edit', 'supply'].includes(statusType)" class="rowSfSpan" @click="handleRowState(row)">
                {{ row.rowState ? $t('输入') : $t('选择') }}
              </span>
            </template>
            <!-- 建筑服务发生地详细地址 -->
            <template #gzfwXxdz="{ row }">
              <el-input
                v-model="row.gzfwXxdz"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 建筑服务项目名称 -->
            <template #gzfwXmmc="{ row }">
              <el-input
                v-model="row.gzfwXmmc"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 建筑服务土地增值税项目编号 -->
            <template #gzfwXmbh="{ row }">
              <el-input
                v-model="row.gzfwXmbh"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 建筑服务跨地市标志 -->
            <template #gzfwKsbz="{ row }">
              <gever-select
                v-model="row.gzfwKsbz"
                number-key
                :clearable="false"
                :options="yesOrNoOptions"
                :disabled="statusType === 'red'"
              />
            </template>
            <!-- 建筑服务跨区域涉税事项报验管理编号 -->
            <template #gzfwKqybybh="{ row }">
              <el-input
                v-model="row.gzfwKqybybh"
                type="text"
                :disabled="statusType === 'red'"
              />
            </template>
            <template #operation="{ row, index }">
              <el-button
                type="text"
                :disabled="statusType === 'red' || ['05', '06'].includes(form.tdyslxDm)"
                @click="handleDeleteSpec(row, index)"
              >
                {{ $t('删除') }}
              </el-button>
            </template>
          </gever-table>
        </div>
      </el-form>
    </div>
    <template v-if="statusType !== 'view'" #footer>
      <el-button @click="handleClose">{{ $t('取 消') }}</el-button>
      <el-button type="primary" :loading="loading" @click="handleSelect">
        {{ $t('保 存') }}
      </el-button>
    </template>
    <!-- 合同项目弹窗 -->
    <contract-item
      v-if="contractItemVisible"
      :dialog-visible.sync="contractItemVisible"
      :contract-type="contractType"
      :party-b="form.payerName"
      @selectData="contractItemSelect"
    ></contract-item>
    <payer-dialog
      v-if="payerDialogVisible"
      :dialog-visible.sync="payerDialogVisible"
      :status="payerStatus"
      :org-id="areaLogin.id"
      @updatelist="selectUpdateList"
    ></payer-dialog>
    <tax-dialog
      v-if="taxVisible"
      :dialog-visible.sync="taxVisible"
      :rate-reason="form.rateReason"
      @updatelist="getTaxDialog"
    ></tax-dialog>
  </gever-dialog>
</template>

<script>
import {
  getIncomeType,
  getInvoiceById,
  getPayerByOrgId,
  getSellerByOrgId,
  invoiceOffset,
  page,
  save,
  selectByTaxpayerType
} from '@/api/financial/invoice/invoiceIssued.js'
import { page as collectionItemPage } from '@/api/financial/invoice/invoiceInformation/collectionItem.js'
import {
  page as payeePage,
  save as payeeSave
} from '@/api/financial/invoice/invoiceInformation/payee.js'
import { save as payerSave } from '@/api/financial/invoice/invoiceInformation/payer.js'
import { createNamespacedHelpers } from 'vuex'
import contractItem from '@/views/financial/invoice/invoiceIssued/components/ContractItem'
import payerDialog from '@/views/financial/invoice/invoiceIssued/components/payerDialog.vue'
import taxDialog from '@/views/financial/invoice/invoiceIssued/components/taxDialog'
import moment from 'moment'
import _ from 'lodash'
import { comboJson } from '@/api/gever/common'

const { mapState } = createNamespacedHelpers('area')

export default {
  name: 'DigitalDetails',
  components: { contractItem, payerDialog, taxDialog },
  props: {
    dialogVisible: { type: Boolean, default: false },
    rowData: {
      type: Object,
      default: () => {}
    },
    statusType: { type: String, default: 'view' },
    invoiceTypeSelect: {
      type: Object,
      default: () => {}
    },
    noticeCode: { type: String, default: '' },
    orgId: { type: String, default: '' }
  },
  data() {
    return {
      remarkLock: false,
      summaryLock: false,
      curIndex: undefined,
      form: {
        rateReason: '',
        totalAmount: 0,
        page: 1,
        rows: 10,
        invoiceKind: 0,
        invoicingStage: 2,
        incomeTypeArr: ['2'],
        tdyslxDm: '',
        invoiceSpecList: [] // 特定业务信息
      },
      rules: {
        payerName: [
          {
            required: true,
            message: '请输入名称',
            trigger: ['blur', 'change']
          }
        ],
        sellerName: [
          {
            required: true,
            message: '请输入名称',
            trigger: ['blur']
          }
        ],
        sellerTaxpayerNo: [
          {
            required: true,
            message: '请输入纳税人识别号',
            trigger: ['blur']
          }
        ],
        payee: [
          {
            required: true,
            message: '请输入信息',
            trigger: ['blur', 'change']
          }
        ],
        reviewer: [
          {
            required: true,
            message: '请输入信息',
            trigger: ['blur', 'change']
          }
        ],
        summary: [
          {
            required: true,
            message: '请输入信息',
            trigger: ['blur', 'change']
          }
        ]
      },
      loading: false,
      tableColumns: [
        { type: 'index', prop: 'sortNum', label: '', width: 35, align: 'center', showOverflowTooltip: false },
        { prop: 'incomeType', headerAlign: 'center', align: 'left', minWidth: 120, label: '*收入类型' },
        { prop: 'taxKindCode', label: '*税收分类', minWidth: 120, align: 'left', showOverflowTooltip: false },
        { prop: 'chargeItem', label: '*收费项目', width: 300, align: 'left' },
        { prop: 'unit', label: '*单位', minWidth: 90, align: 'left' },
        { prop: 'qty', label: '*数量', minWidth: 100, align: 'right' },
        { prop: 'price', label: '*单价', minWidth: 100, align: 'right' },
        { prop: 'saleAmount', label: '*商品/服务金额', minWidth: 120, align: 'right' },
        { prop: 'includeTax', label: '含税', minWidth: 50, align: 'center' },
        { prop: 'taxRate', label: '*税率', minWidth: 60, align: 'center' },
        { prop: 'taxAmount', label: '税额', minWidth: 90, align: 'right' },
        { prop: 'taxableAmount', label: '应税金额', minWidth: 100, align: 'right' },
        { slotName: 'operation', label: '操作', width: 125, fixed: 'right' }
      ],
      table: {
        rows: [],
        total: 0
      },
      invoiceTypeOptions: [],
      optionsCascader: [],
      invoicingStageOptions: [
        { id: 1, text: '先收后开（开票时“已”确认到账）' },
        { id: 2, text: '先开后收（开票时“未”确认到账）' },
        { id: 3, text: '补开（只针对通过“合同管理”直接录入收款信息的情况）' }
      ],
      contractItemVisible: false,
      contractType: '0', // 0每期应收款 1收款记录
      payerDialogVisible: false,
      payerStatus: 'seller',
      // 税收分类
      optionsTaxKindCode: [],
      // 收费项目
      restaurants: [],
      restaurantsPayee: [],
      restaurantsReviewer: [],
      // 税率说明
      taxVisible: false,
      expandOnClickNode: true,
      defaultProps: {
        children: 'children',
        label: 'kindName'
      },
      yesOrNoOptions: [
        { id: 1, text: '是' },
        { id: 0, text: '否' }
      ],
      dqAreaOptions: [
        { id: '广东省-佛山市-禅城区', text: '广东省-佛山市-禅城区' },
        { id: '广东省-佛山市-南海区', text: '广东省-佛山市-南海区' },
        { id: '广东省-佛山市-顺德区', text: '广东省-佛山市-顺德区' },
        { id: '广东省-佛山市-高明区', text: '广东省-佛山市-高明区' },
        { id: '广东省-佛山市-三水区', text: '广东省-佛山市-三水区' }
      ], // 地区下拉
      bdcUnitOptions: [], // 不动产面积单位
      idCardTypeOptions: [], // 旅客证件类型
      vehicleTypeOptions: [], // 交通工具类型
      wayTypeOptions: [], // 运输工具类型
      fjLevelOptions: [], // 飞机交通工具座位等级
      hcLevelOptions: [], // 火车交通工具座位等级
      cbLevelOptions: [], // 船舶交通工具座位等级
      tdywColumns: [], // 特定业务字段
      jzfwColumns: [
        { type: 'index', minWidth: 40, align: 'center' },
        { prop: 'gzfwXmbh', label: '土地增值税项目编号', minWidth: 100 },
        { prop: 'gzfwKsbz', label: '*跨地（市）标志', minWidth: 100 },
        { prop: 'gzfwKqybybh', label: '跨区域涉税事项报验管理编号', minWidth: 120 },
        { prop: 'gzfwXmmc', label: '*建筑项目名称', minWidth: 100 },
        { prop: 'gzfwFsd', label: '*建筑服务发生地', minWidth: 100 },
        { prop: 'gzfwXxdz', label: '详细地址', minWidth: 100 },
        { slotName: 'operation', label: '操作', width: 80, fixed: 'right' }
      ], // 建筑服务字段
      bdcxsColumns: [
        { type: 'index', minWidth: 40, align: 'center' },
        { prop: 'bdcxsDqdz', label: '*不动产地址', minWidth: 100 },
        { prop: 'bdcxsXxdz', label: '*详细地址', minWidth: 100 },
        { prop: 'bdcxsKqbz', label: '*跨地（市）标志', minWidth: 100 },
        { prop: 'bdcxsCqzh', label: '*房屋产权证书', minWidth: 100 },
        { prop: 'bdcxsMjdw', label: '*面积单位', minWidth: 100 },
        { prop: 'bdcxsHtbh', label: '备案合同编号', minWidth: 100 },
        { prop: 'bdcxsXmbh', label: '土地增值税项目编号', minWidth: 100 },
        { prop: 'bdcxsJsjg', label: '核定计税价格', minWidth: 100 },
        { prop: 'bdcxsCjje', label: '实际成交含税金额', minWidth: 100 },
        { slotName: 'operation', label: '操作', width: 80, fixed: 'right' }
      ], // 不动产销售字段
      bdczlColumns: [
        { type: 'index', minWidth: 40, align: 'center' },
        { prop: 'bdcSf', label: '*不动产地址', minWidth: 100 },
        { prop: 'bdcXxdz', label: '*详细地址', minWidth: 100 },
        { prop: 'bdcKsbz', label: '*跨地（市）标志', minWidth: 100 },
        { prop: 'bdcCqjh', label: '*产权证书/证号', minWidth: 100 },
        { prop: 'bdcQsrq', label: '*租赁开始日期', minWidth: 100 },
        { prop: 'bdcJzrq', label: '*租赁结束日期', minWidth: 100 },
        { prop: 'bdcPjdw', label: '*面积单位', minWidth: 100 },
        { slotName: 'operation', label: '操作', width: 80, fixed: 'right' }
      ], // 不动产租赁字段
      hwysColumns: [
        { type: 'index', minWidth: 40, align: 'center' },
        { prop: 'hwysYsgj', label: '*运输工具种类', minWidth: 100 },
        { prop: 'hwysYsgjth', label: '*运输工具牌号', minWidth: 100 },
        { prop: 'hwysQyd', label: '*起运地', minWidth: 100 },
        { prop: 'hwysDdd', label: '*到达地', minWidth: 100 },
        { prop: 'hwysYshwmc', label: '*运输货物名称', minWidth: 100 },
        { slotName: 'operation', label: '操作', width: 80, fixed: 'right' }
      ], // 货物运输字段
      lkysColumns: [
        { type: 'index', minWidth: 40, align: 'center' },
        { prop: 'lkysCxr', label: '出行人', minWidth: 100 },
        { prop: 'lkysCxrq', label: '*出行日期', minWidth: 100 },
        { prop: 'lkysCxrzjlx', label: '*出行人证件类型', minWidth: 100 },
        { prop: 'lkysCxrzjhm', label: '*出行人证件号码', minWidth: 100 },
        { prop: 'lkysCfd', label: '*出发地', minWidth: 100 },
        { prop: 'lkysDdd', label: '*到达地', minWidth: 100 },
        { prop: 'lkysJtgjlx', label: '*交通工具类型', minWidth: 100 },
        { prop: 'lkysDj', label: '*等级', minWidth: 100 },
        { slotName: 'operation', label: '操作', width: 80, fixed: 'right' }
      ], // 旅客运输字段
      tdywName: '', // 特定业务名称
      tdywOptions: [] // 特定业务类型
    }
  },
  computed: {
    ...mapState(['areaLogin']), // 地区机构数据
    // 有小数就显示多少位小数
    comma0() {
      return (val) => {
        if (val) {
          val = String(val)
          // 如果没有小数点则代表整数，直接返回
          if (val.indexOf('.') === -1) {
            return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
          }
          // 有小数点，但是小数点后面都是0，也是直接返回
          if (val.split('.')[1] * 1 === 0) {
            return val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
          }
          // 小数点后面只有一位小数
          if (
            (('0.' + val.split('.')[1]) * 1).toString().split('.')[1].length ===
            1
          ) {
            return (
              val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
              '.' +
              (('0.' + val.split('.')[1]) * 1).toString().split('.')[1]
            )
          }
          // 整数要在千分位加个英文逗号，再拼接小数返回
          return (
            val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
            '.' +
            (('0.' + val.split('.')[1]) * 1).toString().split('.')[1]
          )
        } else {
          return '0'
        }
      }
    },
    // 9位小数
    comma9() {
      return (val, keep_dec = 9) => {
        if (val) {
          if (keep_dec) {
            val = Number(val).toFixed(keep_dec)
          }
          val = String(val)
          // 如果没有小数点则代表整数，直接返回
          if (val.indexOf('.') === -1) {
            return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') + '.00'
          }
          // 有小数点，但是小数点后面都是0，也是直接返回
          if (val.split('.')[1] * 1 === 0) {
            return (
              val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') + '.00'
            )
          }
          // 小数点后面只有一位小数，则补全一个0
          if (
            (('0.' + val.split('.')[1]) * 1).toString().split('.')[1].length ===
            1
          ) {
            return (
              val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
              '.' +
              (('0.' + val.split('.')[1]) * 1).toString().split('.')[1] +
              '0'
            )
          }
          // 整数要在千分位加个英文逗号，再拼接小数返回
          return (
            val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
            '.' +
            (('0.' + val.split('.')[1]) * 1).toString().split('.')[1]
          )
        } else {
          return '0.00'
        }
      }
    },
    // 转浮点数
    comma() {
      return (val, keep_dec = 2) => {
        if (val) {
          if (keep_dec) {
            val = Number(val).toFixed(keep_dec)
          }
          val = String(val)
          if (val.indexOf('.') === -1) {
            return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
          }
          return (
            val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
            '.' +
            val.split('.')[1]
          )
        } else {
          return '0.00'
        }
      }
    },
    // 只保留小数
    thousand() {
      return (val, keep_dec = 2) => {
        if (val) {
          val = Number(val).toFixed(keep_dec)
          return val
        } else {
          return 0.0
        }
      }
    },
    nowTime() {
      return (status) => {
        // 创建 Date 对象
        const date = new Date()
        // 获取年月日
        const year = date.getFullYear()
        const month = date.getMonth() + 1 // 月份从0开始，需要加1
        let day
        if (status === 'start') {
          day = 1
        } else if (status === 'end') {
          day = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
        } else {
          day = date.getDate()
        }
        // 格式化为 yyyy-mm-dd
        return `${year}-${month.toString().padStart(2, '0')}-${day
          .toString()
          .padStart(2, '0')}`
      }
    }
  },
  watch: {
    'table.rows': {
      handler(val) {
        if (
          val.length &&
          val.some((cur) => cur.incomeType && cur.incomeType.includes('1'))
        ) {
          this.$set(this.form, 'incomeTypeArr', ['1'])
          this.$set(this.form, 'incomeType', '1')
        } else {
          this.$set(this.form, 'incomeTypeArr', ['2'])
          this.$set(this.form, 'incomeType', '2')
        }
        this.$set(this.form, 'incomeSourceId', '')
      },
      deep: true,
      immediate: true
    },
    statusType: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.getOptions()
        if (newVal === 'view') {
          this.getList()
          this.title = '查看'
        } else if (newVal === 'edit') {
          this.getList()
          this.title = '编辑'
        } else if (newVal === 'red') {
          this.title = '红冲'
          this.getList()
          if (this.noticeCode) {
            this.form.noticeCode = this.noticeCode
          }
        } else if (newVal === 'supply') {
          this.title = '补开'
          this.form.drawer = this.$store.state.user.name
          this.form.invoicingStage = 3
        } else {
          this.title = '新增'
          this.form.drawer = this.$store.state.user.name
          this.form.invoicingStage = 2
        }
      }
    },
    invoiceTypeSelect: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.$set(this.form, 'invoiceTypeId', newVal.id)
        this.$set(this.form, 'invoiceMedia', newVal.media)
        this.$set(this.form, 'invoiceKind', newVal.kind)
        this.$set(this.form, 'invoiceAbbr', newVal.abbr)
        this.$set(this.form, 'invoiceCode', '')
        this.$set(this.form, 'invoiceNo', '')
        this.$set(this.form, 'invoiceNumber', 0)
        this.$set(this.form, 'batchNo', 1)
        this.$set(this.form, 'numberLength', newVal.numberLength)
      }
    }
  },
  mounted() {
    // 新开或补开时，如果明细小于3条，则补够3条
    if (this.statusType === 'add' || this.statusType === 'supply') {
      const rowLength = this.table.rows.length || 0
      for (
        let i = rowLength;
        i < (this.invoiceTypeSelect.detailSize || 3);
        i++
      ) {
        this.table.rows.push({
          sortNum: '',
          taxKindCode: '',
          chargeItem: '',
          unit: '',
          qty: '',
          price: '',
          saleAmount: '',
          includeTax: 0,
          taxRate: '',
          taxAmount: '',
          taxableAmount: '',
          incomeType: ['2']
        })
        if (['05', '06'].includes(this.form.tdyslxDm)) {
          this.form.invoiceSpecList.push({
            rowState: true,
            bdcQsrq: '',
            bdcJzrq: '',
            bdckSbz: 0,
            gzfwKsbz: 0,
            bdcxsKqbz: 0
          })
        }
      }
    }
  },
  methods: {
    handleOptionsCascader(paramsIncomeType) {
      const incomeType = paramsIncomeType || this.form.incomeType
      let optionsCascader = _.cloneDeep(this.optionsCascader)
      if (
        optionsCascader.length &&
        incomeType.length &&
        !incomeType?.includes('1')
      ) {
        optionsCascader = optionsCascader.map((cur) => {
          cur.disabled = cur.id === '1'
          return cur
        })
      }
      return optionsCascader
    },
    handleInputChange(value) {
      if (value.toString().length > 5) {
        this.form.reviewer = this.form.reviewer.toString().slice(0, 5)
      }
    },
    handleInputChange1(value) {
      if (value.toString().length > 5) {
        this.form.payee = this.form.payee.toString().slice(0, 5)
      }
    },
    getList() {
      getInvoiceById({ id: this.rowData.id }).then((res) => {
        this.form = {
          ...res.data,
          invoiceDetailItemList: (res?.data?.invoiceDetailItemList || []).map(
            (cur) => {
              const incomeTypeArr = []
              if (cur.incomeType) incomeTypeArr.push(`${cur.incomeType}`)
              if (cur.incomeSourceId) incomeTypeArr.push(cur.incomeSourceId)
              cur.incomeType = incomeTypeArr
              return cur
            }
          )
        }
        this.form.incomeTypeArr = []
        this.form.incomeType
          ? this.form.incomeTypeArr.push(this.form.incomeType.toString())
          : ''
        this.form.incomeSourceId
          ? this.form.incomeTypeArr.push(this.form.incomeSourceId.toString())
          : ''
        this.handleOptionsCascader(this.form.incomeType)
        this.getTdywInfo()
        this.form.invoiceSpecList.forEach((spec) => {
          if (['03', '05', '06'].includes(this.form.tdyslxDm)) {
            if (spec.gzfwFsd?.startsWith('广东省-') || spec.bdcSf?.startsWith('广东省-') || spec.bdcxsDqdz?.startsWith('广东省-')) {
              spec.rowState = true
            }
          }
        })
        this.table.rows = this.form.invoiceDetailItemList
        if (this.statusType === 'red') {
          this.table.rows.forEach((item) => {
            item.saleAmount = item.offsetSaleAmount
            item.taxAmount = item.offsetTaxAmount
            item.taxableAmount = item.offsetTaxableAmount
            item.totalAmount = item.offsetTotalAmount
          })
        }
        this.table.rows.forEach((item) => {
          for (const key in item) {
            if (['saleAmount'].includes(key)) {
              item[key] = this.thousand(item[key])
            }
            if (['price'].includes(key)) {
              const price = item[key]
              if (
                !price.toString().includes('.') ||
                price.toString().split('.')[1].length === 1
              ) {
                item[key] = Number(item[key]).toFixed(2)
              }
            }
          }
        })
        if (this.statusType === 'red') {
          this.form.offsetSourceCode = this.form.invoiceCode
          this.form.offsetSourceNo = this.form.invoiceNo
          this.$set(this.form, 'offsetSourceId', this.rowData.id)
          if (this.noticeCode) {
            this.form.noticeCode = this.noticeCode
          }
          this.$set(this.form, 'invoiceTyId', '')
          this.$set(this.form, 'invoiceCode', '')
          this.$set(this.form, 'invoiceNo', '')
          this.$set(this.form, 'id', '')
          if (this.statusType === 'view' || this.statusType === 'edit') {
            this.getInventory(this.invoiceTypeSelect.id)
          }
        }
        // 查看时，明细数字的千分位
        if (this.statusType === 'view') {
          this.table.rows.forEach((item) => {
            item.qty = this.comma0(item.qty)
            item.price = this.comma9(item.price)
            item.saleAmount = this.comma(item.saleAmount)
          })
        }
        // 编辑时，如果明细小于3条，则补够3条
        if (this.statusType === 'edit') {
          const rowLength = this.table.rows.length || 0
          for (
            let i = rowLength;
            i < (this.invoiceTypeSelect.detailSize || 3);
            i++
          ) {
            this.table.rows.push({
              sortNum: '',
              taxKindCode: '',
              chargeItem: '',
              unit: '',
              qty: '',
              price: '',
              saleAmount: '',
              includeTax: 0,
              taxRate: '',
              taxAmount: '',
              taxableAmount: '',
              incomeType: ['2']
            })
            if (['05', '06'].includes(this.form.tdyslxDm)) {
              this.form.invoiceSpecList.push({
                rowState: true,
                bdcQsrq: '',
                bdcJzrq: '',
                bdckSbz: 0,
                gzfwKsbz: 0,
                bdcxsKqbz: 0
              })
            }
          }
        }
      })
    },
    async getCashierCommonInfo(queryString, cb) {
      const params = {
        orgId: this.areaLogin?.id,
        payerName: this.form.payerName
      }
      const { data } = await getPayerByOrgId(params)
      const result = queryString
        ? data.filter(
            ({ payerName = '' }) => payerName.indexOf(queryString) > -1
          )
        : data
      cb(result)
    },
    handlePayerInfo(val) {
      this.$set(this.form, 'payerName', val.payerName)
      this.$set(this.form, 'payerTaxpayerNo', val.taxpayerNo)
      this.$set(this.form, 'payerContactInfor', val.contactInfor)
      this.$set(this.form, 'payerAccountInfor', val.accountInfor)
      this.$set(this.form, 'payerEmail', val.email)
      this.$set(this.form, 'payerPhone', val.phone)
    },
    getInventory(id) {
      const params1 = {
        orgId: this.areaLogin.id || this.orgId,
        invoiceTypeId: id
      }
      page(params1).then((res) => {
        this.invoiceTypeOptions = res.data.rows
        if (res.data.rows.length === 1) {
          this.handleChangeInvoiceType(res.data.rows[0].id)
          this.$set(this.form, 'invoiceTyId', res.data.rows[0].id)
        }
      })
    },
    getOptions() {
      comboJson({ path: '/financial/invoice/digital/tdyslxdm/' }).then((res) => {
        this.tdywOptions = res.data
      })
      comboJson({ path: '/financial/invoice/digital/bdcDw/' }).then((res) => {
        this.bdcUnitOptions = res.data
      })
      comboJson({ path: '/financial/invoice/digital/sfzlx/' }).then((res) => {
        this.idCardTypeOptions = res.data
      })
      comboJson({ path: '/financial/invoice/digital/cxgjlx/' }).then((res) => {
        this.vehicleTypeOptions = res.data
      })
      comboJson({ path: '/financial/invoice/digital/ysgjzl/' }).then((res) => {
        this.wayTypeOptions = res.data
      })
      comboJson({ path: '/financial/invoice/digital/cxgjlxDj/' }).then((res) => {
        res.data.forEach((item) => {
          if (['1', '2', '3'].includes(item.id)) {
            this.fjLevelOptions.push(item)
          }
          if (['4', '5', '6', '7'].includes(item.id)) {
            this.hcLevelOptions.push(item)
          }
          if (['8', '9', '10'].includes(item.id)) {
            this.cbLevelOptions.push(item)
          }
        })
      })
      if (this.statusType === 'view') {
        getIncomeType().then((res) => {
          this.optionsCascader = res.data
        })
        selectByTaxpayerType({ orgId: this.areaLogin.id || this.orgId }).then(
          (res) => {
            this.optionsTaxKindCode = res.data
          }
        )
        return
      }
      if (this.statusType !== 'view' || this.statusType !== 'edit') {
        this.getInventory(this.invoiceTypeSelect.id)
      }

      getIncomeType().then((res) => {
        this.optionsCascader = res.data
      })
      selectByTaxpayerType({ orgId: this.areaLogin.id || this.orgId }).then(
        (res) => {
          this.optionsTaxKindCode = res.data
        }
      )
      collectionItemPage({ orgId: this.areaLogin.id || this.orgId }).then(
        (res) => {
          this.restaurants = res.data.rows
        }
      )
      if (this.statusType !== 'red') {
        payeePage({
          orgId: this.areaLogin.id || this.orgId,
          operatorType: 1
        }).then((res) => {
          this.restaurantsPayee = res.data.rows
          if (res.data.rows.length > 0) {
            this.$set(this.form, 'payee', res.data.rows[0].operatorName)
          }
        })
        payeePage({
          orgId: this.areaLogin.id || this.orgId,
          operatorType: 2
        }).then((res) => {
          this.restaurantsReviewer = res.data.rows
          if (res.data.rows.length > 0) {
            this.$set(this.form, 'reviewer', res.data.rows[0].operatorName)
          }
        })

        getSellerByOrgId({ orgId: this.areaLogin.id || this.orgId }).then(
          (res) => {
            res.data.nowStatus = 'seller'
            this.selectUpdateList(res.data)
          }
        )
      }
    },
    /* 税收分类选择框 */
    handleTaxKind(val, index) {
      if (!val.taxRate) {
        return
      }
      this.table.rows[index].taxKindCode = val.kindCode
      this.$refs[`selectTree${index}`].blur()
      // this.table.rows[index].includeTax = val.taxRate ? 1 : 0
      this.table.rows[index].taxRate = val.taxRate
      this.table.rows[index].taxKindName = val.kindName
      this.handleTaxRate(this.table.rows[index].taxRate, index)
      if (this.form.incomeType === '1') {
        (this.table.rows[index]?.invoiceDetailItemRelList || []).forEach(
          (item) => {
            const temp = this.getTaxComputed({
              saleAmount: Number(
                item.saleAmount.toString().replaceAll(',', '')
              ),
              includeTax: this.table.rows[index].includeTax,
              taxRate: this.table.rows[index].taxRate
            })
            item.includeTax = temp.includeTax
            item.taxAmount = temp.taxAmount
            item.totalAmount = temp.totalAmount
            item.taxRate = temp.taxRate
          }
        )
      }
    },
    formatData(data) {
      let options = []
      data.forEach((item) => {
        if (item.children) {
          options = options.concat(this.formatData(item.children))
        }
        options.push({ label: item.kindName, value: item.kindCode })
      })
      return options
    },
    /* 收费项目 */
    querySearch(queryString, cb) {
      var restaurants = this.restaurants
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString, 'itemName'))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString, text) {
      return (restaurant) => {
        return restaurant[text].indexOf(queryString) === 0
      }
    },
    /* 收款人 */
    getFetchSuggestions(queryString, cb) {
      var restaurants = this.restaurantsPayee
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString, 'operatorName'))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    /* 复核人 */
    getFetchSuggestions2(queryString, cb) {
      var restaurants = this.restaurantsReviewer
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString, 'operatorName'))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    /* 商品/服务金额 */
    handleSaleAmountInput(val, index) {
      if (this.table.rows[index].taxRate * 1 === -1) {
        this.table.rows[index].taxAmount = 0
        this.table.rows[index].taxableAmount = 0
        this.table.rows[index].totalAmount = val
        return
      }
      const temp = this.getTaxComputed({
        saleAmount: val,
        includeTax: this.table.rows[index].includeTax,
        taxRate: this.table.rows[index].taxRate
      })
      this.table.rows[index].taxAmount = temp.taxAmount
      this.table.rows[index].taxableAmount = temp.taxableAmount
      this.table.rows[index].totalAmount = temp.totalAmount
      this.getSum()
    },
    /* 含税 */
    handleIncludeTax(val, index) {
      if (this.table.rows[index].taxRate * 1 === -1) {
        this.table.rows[index].taxAmount = 0
        this.table.rows[index].taxableAmount = 0
        this.table.rows[index].totalAmount = this.table.rows[index].saleAmount
        this.table.rows[index].totalAmount = Number(
          this.table.rows[index].saleAmount.toString().replaceAll(',', '')
        )
        this.getSum()
        return
      }
      const temp = this.getTaxComputed({
        saleAmount: Number(
          this.table.rows[index].saleAmount.toString().replaceAll(',', '')
        ),
        includeTax: val,
        taxRate: this.table.rows[index].taxRate
      })
      this.table.rows[index].taxAmount = temp.taxAmount
      this.table.rows[index].taxableAmount = temp.taxableAmount
      this.table.rows[index].totalAmount = temp.totalAmount
      if (this.form.incomeType === '1') {
        this.table.rows[index].invoiceDetailItemRelList.forEach((item) => {
          const temp = this.getTaxComputed({
            saleAmount: Number(item.saleAmount.toString().replaceAll(',', '')),
            includeTax: this.table.rows[index].includeTax,
            taxRate: this.table.rows[index].taxRate
          })
          item.includeTax = temp.includeTax
          item.taxRate = temp.taxRate
          item.taxAmount = temp.taxAmount
          item.totalAmount = temp.totalAmount
        })
      }
    },
    /* 税率 */
    handleTaxRate(val, index) {
      if (val * 1 === -1) {
        this.table.rows[index].taxAmount = 0
        this.table.rows[index].taxableAmount = Number(
          this.table.rows[index].saleAmount.toString().replaceAll(',', '')
        )
        this.table.rows[index].totalAmount = Number(
          this.table.rows[index].saleAmount.toString().replaceAll(',', '')
        )
        return
      }
      if (val * 1 === 1 || val * 1 === 3) {
        this.taxVisible = true
      }
      const temp = this.getTaxComputed({
        saleAmount: Number(
          this.table.rows[index].saleAmount.toString().replaceAll(',', '')
        ),
        includeTax: this.table.rows[index].includeTax,
        taxRate: val
      })
      this.table.rows[index].taxAmount = temp.taxAmount
      this.table.rows[index].taxableAmount = temp.taxableAmount
      this.table.rows[index].totalAmount = temp.totalAmount
      if (this.table.rows[index]?.invoiceDetailItemRelList?.length) {
        this.table.rows[index]?.invoiceDetailItemRelList.forEach((item, i) => {
          const {
            includeTax,
            saleAmount,
            taxRate,
            taxAmount,
            taxableAmount,
            totalAmount
          } = this.getTaxComputed({
            saleAmount: Number(item.saleAmount.toString().replaceAll(',', '')),
            includeTax: item.includeTax,
            taxRate: val
          })
          item.saleAmount = saleAmount
          item.includeTax = includeTax
          item.taxRate = taxRate
          item.taxAmount = taxAmount
          item.taxableAmount = taxableAmount
          item.totalAmount = totalAmount
          item.taxRate = taxRate
        })
      }
    },
    /* 选择开具其他税率发票的理由 */
    getTaxDialog(val) {
      this.form.rateReason = val
    },
    /* 付款方，收款方 */
    selectUpdateList(val) {
      if (val.nowStatus === 'seller') {
        this.$set(this.form, 'sellerName', val.orgName)
        this.$set(this.form, 'sellerTaxpayerNo', val.taxpayerNo)
        this.$set(this.form, 'sellerContactInfor', val.contactInfor)
        this.$set(this.form, 'sellerAccountInfor', val.accountInfor)
        this.$set(this.form, 'sellerTaxpayerType', val.taxpayerType)
      } else if (val.nowStatus === 'payer') {
        this.$set(this.form, 'payerName', val.payerName)
        this.$set(this.form, 'payerTaxpayerNo', val.taxpayerNo)
        this.$set(this.form, 'payerContactInfor', val.contactInfor)
        this.$set(this.form, 'payerAccountInfor', val.accountInfor)
        this.$set(this.form, 'payerEmail', val.email)
        this.$set(this.form, 'payerPhone', val.phone)
      }
    },
    /* 保存收款方信息 */
    savePayer() {
      if (!this.form.payerName) {
        return this.$message.warning('付款方名称不能为空')
      }
      if (!this.form.payerTaxpayerNo) {
        return this.$message.warning('付款方纳税人识别号不能为空')
      }
      const params = {
        orgId: this.areaLogin.id,
        payerName: this.form.payerName || '',
        taxpayerNo: this.form.payerTaxpayerNo || '',
        contactInfor: this.form.payerContactInfor || '',
        accountInfor: this.form.payerAccountInfor || '',
        email: this.form.payerEmail || '',
        phone: this.form.payerPhone || ''
      }
      this.loading = true
      payerSave(params)
        .then((res) => {
          this.$message.success(res.message)
        })
        .finally(() => {
          this.loading = false
        })
    },
    /* 库存发票： */
    handleChangeInvoiceType(val) {
      const nowSelect = this.invoiceTypeOptions.filter(
        (item) => item.id === val
      )[0]
      this.form.orgId = this.areaLogin.id || this.orgId
      this.form.invoiceMedia = nowSelect.invoiceMedia
      this.form.invoiceKind = nowSelect.invoiceKind
      this.form.invoiceAbbr = nowSelect.invoiceAbbr
      this.form.invoiceCode = nowSelect.invoiceCode
      this.form.invoiceNo = nowSelect.beginNo
      this.form.invoiceNumber = nowSelect.beginSeq
      this.form.batchNo = nowSelect.batchNo
      this.form.numberLength = nowSelect.numberLength
    },
    /* 删除 */
    handleDelete(row, index) {
      this.table.rows.splice(index, 1)
      if (['05', '06'].includes(this.form.tdyslxDm)) {
        this.form.invoiceSpecList.splice(index, 1)
      }
      this.getRemark()
      this.getSummary()
      this.$forceUpdate()
    },
    handleBlur(row, index) {
      this.getSummary()
    },
    /* 收款人：复核人：保存 */
    handleSelectPrayer(val) {
      if (val === 'reviewer') {
        if (!this.form.reviewer.replace(/\s*/g, '')) {
          return this.$message.warning('请输入内容再点击保存')
        }
        const params = {
          operatorName: this.form.reviewer,
          orgId: this.areaLogin.id || this.orgId,
          operatorType: 2
        }
        payeeSave(params).then((res) => {
          this.$message.success(res.message)
        })
      } else if (val === 'payee') {
        if (!this.form.payee.replace(/\s*/g, '')) {
          return this.$message.warning('请输入内容再点击保存')
        }
        const params = {
          operatorName: this.form.payee,
          orgId: this.areaLogin.id || this.orgId,
          operatorType: 1
        }
        payeeSave(params).then((res) => {
          this.$message.success(res.message)
        })
      }
    },
    /* 关闭弹窗 */
    handleClose() {
      if (this.statusType !== 'view') {
        this.$emit('updatelist')
      }
      this.$emit('update:dialogVisible', false)
    },
    /* 确认按钮 */
    handleSelect() {
      this.$refs['_formRef'].validate((valid) => {
        if (valid) {
          // 把千分位去掉 空行不保存
          const realRow = []
          for (const item of this.table.rows) {
            if (item.chargeItem) {
              if (
                item.qty &&
                item.price &&
                item.unit &&
                item.taxKindCode &&
                item.taxRate.toString() &&
                item.saleAmount
              ) {
                realRow.push(item)
              } else {
                return this.$message.warning(
                  '请完善开票信息的*税收分类、*收款项目、*单位、*数量、*单价、*商品/服务金额和*税率必填项'
                )
              }
            }
          }
          if (realRow.length === 0) {
            return this.$message.warning('请完善至少一行*开票信息')
          }
          if (realRow.length > (this.invoiceTypeSelect.detailSize || 3)) {
            return this.$message.warning(this.$t('收款明细数量不能大于3'))
          }
          if (this.statusType !== 'red') {
            if (
              realRow.some(
                (item) =>
                  item.incomeType.length === 1 && item.incomeType[0] === '2'
              )
            ) {
              return this.$message.warning(
                '收入类型为非合同收入时必须选择非合同收入的明细项'
              )
            }
          }
          if (
            this.form.incomeTypeArr.includes('1') &&
            realRow.every((cur) => cur.incomeType.includes('2'))
          ) {
            return this.$message.warning(
              this.$t('当收款明细全部为非合同收入时,主体收入类型不能为合同收入')
            )
          }
          if (
            this.form.incomeTypeArr.includes('2') &&
            realRow.every((cur) => cur.incomeType.includes('1'))
          ) {
            return this.$message.warning(
              this.$t('当收款明细全部为非合同收入时,主体收入类型不能为合同收入')
            )
          }
          // 特定业务的发票需要完善相关信息
          const tdyw = this.form.tdyslxDm || ''
          if (tdyw !== '') {
            // 特定业务行数(invoiceSpecList)要与发票明细行数(realRow)一样
            if (['05', '06'].includes(tdyw) && this.form.invoiceSpecList.length > realRow.length) {
              // 从后面开始删除多出的行
              while (this.form.invoiceSpecList.length > realRow.length) {
                this.form.invoiceSpecList.pop() // 删除最后一行
              }
            }
            if (this.form.invoiceSpecList.length === 0) {
              return this.$message.warning(
                this.$t('请完善特定业务相关信息')
              )
            }
          }
          this.loading = true
          const params = {
            ...this.form,
            orgId: this.orgId,
            taxableAmount: realRow.reduce(
              (t, c) =>
                (t += c.taxableAmount.toString().replaceAll(',', '') * 1 || 0),
              0
            ),
            saleAmount: realRow.reduce(
              (t, c) =>
                (t += c.saleAmount.toString().replaceAll(',', '') * 1 || 0),
              0
            ),
            incomeType: this.form.incomeTypeArr[0],
            incomeSourceId:
              this.form.incomeTypeArr.length > 1
                ? this.form.incomeTypeArr[1]
                : ''
          }
          params.invoiceDetailItemList = realRow.map((cur) => {
            if (cur.incomeType.length > 1) {
              cur.incomeSourceId = cur.incomeType[1]
            } else {
              cur.incomeSourceId = ''
            }
            cur.incomeType = cur.incomeType[0]
            // 去掉千分位
            cur.qty = cur.qty.toString().replaceAll(',', '')
            cur.price = cur.price.toString().replaceAll(',', '')
            cur.saleAmount = cur.saleAmount.toString().replaceAll(',', '')
            cur.taxAmount = cur.taxAmount.toString().replaceAll(',', '')
            cur.taxableAmount = cur.taxableAmount.toString().replaceAll(',', '')
            cur.totalAmount = cur.totalAmount.toString().replaceAll(',', '')
            return cur
          })
          if (this.statusType !== 'red') {
            save(params)
              .then((res) => {
                this.$emit('updateList')
                this.$emit('update:dialogVisible', false)
                this.$message.success(res.message)
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            invoiceOffset(params)
              .then((res) => {
                this.$emit('updateList')
                this.$emit('update:dialogVisible', false)
                this.$message.success(res.message)
              })
              .finally(() => {
                this.loading = false
              })
          }
        }
      })
    },
    // 数量失焦
    qtyBlur(e) {
      const reg = /^([1-9]\d*|0)(\.\d+)?$/
      const value = e.target.value.toString().replaceAll(',', '')
      if (reg.test(value)) {
        const str = value + ''
        // 判断小数点后的长度
        if (str.includes('.')) {
          const len = str.split('.')[1]
          if (len * 1 === 0) {
            e.target.value = value * 1
          } else {
            if (len.length > 9) {
              e.target.value = (value * 1).toFixed(9)
            } else {
              e.target.value = value * 1
            }
          }
        } else {
          e.target.value = value * 1
        }
      } else {
        e.target.value = 0
      }
      e.target.value = this.comma0(e.target.value)
    },
    // 单价失焦
    unitPriceBlur(e, precision = 2) {
      // 最终结果，单价可以有九位小数
      const reg = /^([1-9]\d*|0)(\.\d+)?$/
      const value = e.target.value.toString().replaceAll(',', '')
      if (reg.test(value)) {
        const str = value + ''
        // 判断小数点后的长度
        if (str.includes('.')) {
          const len = str.split('.')[1]
          if (len * 1 === 0) {
            e.target.value = (value * 1).toFixed(precision)
          } else {
            if (len.length > 9) {
              e.target.value = (value * 1).toFixed(9)
            } else {
              e.target.value = value * 1
            }
          }
        } else {
          e.target.value = (value * 1).toFixed(precision)
        }
      } else {
        e.target.value = (0).toFixed(precision)
      }
      e.target.value = this.comma9(e.target.value)
    },
    // 金额失焦
    inputBlur(e, precision = 2) {
      const reg =
        /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/
      const value = e.target.value.toString().replaceAll(',', '')
      if (reg.test(value)) {
        e.target.value = (value * 1).toFixed(precision)
      } else {
        e.target.value = (0).toFixed(precision)
      }
      e.target.value = this.comma(e.target.value)
    },
    /* 表格合计 */
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 3) {
          sums[index] = '合计:'
          return
        }
        // 数量
        if (index === 5) {
          const values = data.map((item) =>
            Number(item[column.property].toString().replaceAll(',', ''))
          )
          const sumVal = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          this.form[column.property] = sumVal
          sums[index] = this.comma0(sumVal)
        }
        // 单价的单独处理，最大可支持9位小数
        if (index === 6) {
          const values = data.map((item) =>
            Number(item[column.property].toString().replaceAll(',', ''))
          )
          const sumVal = this.roundFractional(
            values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0),
            9
          )
          this.form[column.property] = sumVal
          sums[index] = this.comma9(sumVal)
        }
        // 商品/服务金额
        if (index === 7) {
          const values = data.map((item) =>
            Number(item[column.property].toString().replaceAll(',', ''))
          )
          const sumVal = this.roundFractional(
            values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
          )
          this.form[column.property] = sumVal
          sums[index] = this.comma(sumVal)
        }
        // 税率，如果是免税，要显示为0
        if (index === 9) {
          const values = data.map((item) => Number(item[column.property]))
          sums[index] = this.roundFractional(
            values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                if (value === -1) {
                  return prev + 0
                } else {
                  return prev + curr
                }
              } else {
                return prev
              }
            }, 0)
          )
          this.form[column.property] = sums[index]
          sums[index] = this.comma(sums[index])
        }
        // 税额
        if (index === 10) {
          const values = data.map((item) => Number(item[column.property]))
          sums[index] = this.roundFractional(
            values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
          )
          this.form[column.property] = sums[index]
          sums[index] = this.comma(sums[index])
        }
        // 应税金额
        if (index === 11) {
          const values = data.map((item) => Number(item[column.property]))
          sums[index] = this.roundFractional(
            values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
          )
          this.form[column.property] = sums[index]
          sums[index] = this.comma(sums[index])
        }
      })
      this.getSum()
      this.$set(this.form, 'totalAmount', this.form.totalAmount)
      return sums
    },
    /* 选择合同项目 */
    handleContract(index) {
      if (this.form.invoicingStage !== 3) {
        this.contractType = '0'
      } else {
        this.contractType = '1'
      }
      this.curIndex = index
      this.contractItemVisible = true
    },
    /* 添加 */
    handleContractAdd() {
      const item = {
        sortNum: '',
        taxKindCode: '',
        chargeItem: '',
        unit: '',
        qty: '',
        price: '',
        saleAmount: '',
        includeTax: 0,
        taxRate: '',
        taxAmount: '',
        taxableAmount: '',
        incomeType: ['2']
      }
      this.table.rows.push(item)
      if (['05', '06'].includes(this.form.tdyslxDm)) {
        this.form.invoiceSpecList.push({
          rowState: true,
          bdcQsrq: '',
          bdcJzrq: '',
          bdckSbz: 0,
          gzfwKsbz: 0,
          bdcxsKqbz: 0
        })
      }
    },
    /* 选择合同弹窗回调 */
    contractItemSelect(val) {
      if (this.isRepetition(val)) {
        return this.$message.warning('选择重复，请重新选择')
      }
      let listData = val.reduce((acc, item) => {
        if (acc[item.contractCode + item.expenseName]) {
          acc[item.contractCode + item.expenseName].push(item)
        } else {
          acc[item.contractCode + item.expenseName] = [item]
        }
        return acc
      }, {})
      listData = this.splitRow(listData)

      for (const key in listData) {
        const nowListItem = {
          sortNum: '',
          taxKindCode: '',
          chargeItem: '',
          unit: '',
          qty: '',
          price: '',
          saleAmount: '',
          includeTax: 0,
          taxRate: '',
          taxAmount: '',
          taxableAmount: '',
          contractCode: '',
          originalContractCode: ''
        }
        if (this.form.invoicingStage === 3) {
          nowListItem.saleAmount = this.comma(
            listData[key].reduce((acc, val) => {
              return (acc += val.currentInvoiceMoney)
            }, 0)
          )
          nowListItem.chargeItem = this.concatenatedString(listData[key])

          this.isMore
            ? (nowListItem.textTemp = listData[key][0].contractCode)
            : ''
          nowListItem.invoiceDetailItemRelList = listData[key].map((item) => {
            return {
              relId: item.receivableDetailId,
              contractId: item.contractId,
              contractCode: item.contractCode,
              receiveId: item.receiveId,
              saleAmount: item.currentInvoiceMoney.toFixed(2),
              includeTax: '',
              taxRate: '',
              taxAmount: '',
              totalAmount: ''
            }
          })
        } else {
          nowListItem.saleAmount = this.comma(
            listData[key].reduce((acc, val) => {
              return (acc += val.relAmount)
            }, 0)
          )
          // nowListItem.saleAmount = nowListItem.saleAmount.toFixed(2)
          nowListItem.chargeItem = this.concatenatedString(listData[key])
          this.isMore
            ? (nowListItem.textTemp = listData[key][0].contractCode)
            : ''
          nowListItem.invoiceDetailItemRelList = listData[key].map((item) => {
            return {
              // relAmount: item.relAmount
              relId: item.receivableDetailId,
              contractId: item.contractId,
              contractCode: item.contractCode,
              saleAmount: item.relAmount.toFixed(2),
              includeTax: '',
              taxRate: '',
              taxAmount: '',
              totalAmount: ''
            }
          })
        }
        nowListItem.contractCode = listData[key][0].contractCode
        nowListItem.originalContractCode = listData[key][0].originalContractCode
        nowListItem.incomeType = '1'
        nowListItem.amount = this.comma(nowListItem.amount)

        this.table.rows.splice(
          key === '0' ? this.curIndex : this.curIndex + key * 1,
          key === '0' ? 1 : 0,
          nowListItem
        )
      }
      // 判断明细行的数量，如果大于3则截取后面的行
      const detailSize = this.invoiceTypeSelect.detailSize || 3
      let rowCount = 0
      const realRows = []
      this.table.rows.forEach((item) => {
        if (item.chargeItem && item.chargeItem !== '') {
          rowCount++
          realRows.push(item)
        }
      })
      if (rowCount > detailSize) {
        // 截取第三行以后的
        realRows.splice(detailSize, rowCount - detailSize)
        this.table.rows = realRows
        this.table.total = this.table.rows.length
        // 提示已截取掉第三行后面的数据
        this.$confirm(
          '明细行数量超过票据类型的最大明细数量' +
            detailSize +
            '，自动截掉超出的行，请确认',
          '提示',
          {
            confirmButtonText: '确认',
            type: 'success',
            showCancelButton: false
          }
        )
      } else if (this.table.rows.length > detailSize) {
        // 去掉超出的空白行
        let diff = this.table.rows.length - detailSize
        for (let i = this.table.rows.length - 1; diff > 0; i--) {
          const item = this.table.rows[i]
          if (!(item.chargeItem && item.chargeItem !== '')) {
            this.table.rows.splice(i, 1)
            diff--
          }
        }
        this.table.total = this.table.rows.length
      }
      this.$nextTick(() => {
        // 获取备注
        this.getRemark()
        this.getSummary()
      })
    },
    getRemark() {
      // 已锁定的不能赋值
      if (!this.remarkLock) {
        let remarkStr = ''
        if (this.table.rows) {
          this.table.rows.forEach((item) => {
            const rowRemark = item.originalContractCode || item.contractCode
            if (rowRemark) {
              if (!remarkStr.includes(rowRemark)) {
                if (remarkStr !== '') {
                  remarkStr = remarkStr + '，' + rowRemark
                } else {
                  remarkStr = rowRemark
                }
              }
            }
          })
        }
        this.$set(this.form, 'remark', remarkStr)
      }
    },
    getSummary() {
      if (!this.summaryLock) {
        if (this.table.rows && this.table.rows[0]) {
          this.$set(this.form, 'summary', this.table.rows[0].chargeItem)
        } else {
          this.$set(this.form, 'summary', '')
        }
      }
    },
    /* 拼接字符串 */
    concatenatedString(arr) {
      let dates = []
      arr.forEach((cur) => {
        for (const key in cur) {
          if (['receivableStartDate', 'receivableEndDate'].includes(key)) {
            const time = new Date(cur[key]).getTime()
            dates.push(time)
          }
        }
      })
      dates = dates.sort()

      const str =
        '收' +
        arr[0].secondName +
        arr[0].contractName +
        (arr[0].contractTypeName || '') +
        '【' +
        moment(dates[0]).format('YYYY-MM-DD') +
        '到' +
        moment(dates[dates.length - 1]).format('YYYY-MM-DD') +
        '】' +
        arr[0].expenseName
      return str
    },
    /* 判断是否需要 拆分 */
    splitRow(objRow) {
      this.isMore = false
      const obj = JSON.parse(JSON.stringify(objRow))
      const returnData = []
      const sortBy = (field) => {
        return (x, y) => {
          return x[field].split('-')[1] - y[field].split('-')[1]
        }
      }
      for (const key in obj) {
        obj[key].sort(sortBy('receivableEndDate'))
        const tempStr = this.concatenatedString(obj[key])
        if (tempStr.length > 250) {
          returnData.push(obj[key].slice(0, Math.ceil(obj[key].length / 2)))
          returnData.push(obj[key].slice(Math.ceil(obj[key].length / 2)))
          this.isMore = true
        } else {
          returnData.push(obj[key])
        }
      }
      return returnData
    },
    /* 判断是否选择重复 */
    isRepetition(arr) {
      if (this.table.rows.length === 0) return false
      const arrTemp = []
      this.table.rows.map((item) => {
        if (item.invoiceDetailItemRelList) {
          arrTemp.push(...item.invoiceDetailItemRelList)
        }
      })
      const result = arrTemp.filter((item) => {
        return arr.find((prop) => {
          return prop.receivableDetailId == item.relId
        })
      })
      if (result.length !== 0) {
        return true
      } else {
        return false
      }
    },
    /**
     * 小数点后面保留第 n 位
     * @param x 做近似处理的数
     * @param n 小数点后第 n 位
     * @returns 近似处理后的数
     */
    roundFractional(x, n = 2) {
      return Math.round(x * Math.pow(10, n)) / Math.pow(10, n)
    },
    // eslint-disable-next-line complexity
    smallToBig(money) {
      // 汉字的数字
      const cnNums = [
        '零',
        '壹',
        '贰',
        '叁',
        '肆',
        '伍',
        '陆',
        '柒',
        '捌',
        '玖'
      ]
      // 基本单位
      const cnIntRadice = ['', '拾', '佰', '仟']
      // 对应整数部分扩展单位
      const cnIntUnits = ['', '万', '亿', '兆']
      // 对应小数部分单位
      const cnDecUnits = ['角', '分']
      // 整数金额时后面跟的字符
      const cnInteger = '整'
      // 整型完以后的单位
      const cnIntLast = '元'
      // 最大处理的数字
      // eslint-disable-next-line no-loss-of-precision
      const maxNum = 9999999999999999.99
      // 金额整数部分
      let integerNum
      // 金额小数部分
      let decimalNum
      // 输出的中文金额字符串
      let chineseStr = ''
      // 分离金额后用的数组，预定义
      let parts
      if (money === '') {
        return ''
      }
      money = parseFloat(money)
      if (money >= maxNum) {
        // 超出最大处理数字
        return ''
      }
      if (money === 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger
        return chineseStr
      }
      const countMoney = Math.abs(money).toString()
      // 转换为字符串
      money = money.toString()

      if (countMoney.indexOf('.') === -1) {
        integerNum = countMoney

        decimalNum = ''
      } else {
        parts = countMoney.split('.')
        integerNum = parts[0]
        decimalNum = parts[1].substr(0, 4)
      }
      // 获取整型部分转换
      if (parseInt(integerNum, 10)) {
        let zeroCount = 0
        const IntLen = integerNum.length
        for (let i = 0; i < IntLen; i++) {
          const n = integerNum.substr(i, 1)
          const p = IntLen - i - 1
          const q = p / 4
          const m = p % 4
          if (n === '0') {
            zeroCount++
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0]
            }
            // 归零
            zeroCount = 0
            // alert(cnNums[parseInt(n)])
            chineseStr += cnNums[parseInt(n)] + cnIntRadice[m]
          }
          if (m === 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q]
          }
        }
        chineseStr += cnIntLast
      }
      // 小数部分
      if (decimalNum !== '') {
        const decLen = decimalNum.length
        for (let i = 0; i < decLen; i++) {
          const n = decimalNum.substr(i, 1)
          if (n !== '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i]
          }
        }
      }
      if (chineseStr === '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger
      } else if (decimalNum === '') {
        chineseStr += cnInteger
      }

      // if (money < 0) {
      //   return `负值`
      // }
      return money < 0 ? `负${chineseStr}` : chineseStr
    },
    /**
    计算
    saleAmount 商品或服务金额
    includeTax 是否含税0:不含税,1:含税}
    taxRate 税率(引用数据字典，-1表示免税)
    */
    getTaxComputed(val) {
      for (const key in val) {
        val[key] = Number(val[key].toString().replaceAll(',', ''))
      }
      const temp = {
        includeTax: '',
        saleAmount: '',
        taxRate: '',
        taxAmount: '',
        taxableAmount: '',
        totalAmount: ''
      }
      // 含税
      temp.includeTax = val.includeTax
      // 商品或服务金额
      temp.saleAmount = val.saleAmount
      // 税率
      temp.taxRate = val.taxRate
      if (val.includeTax) {
        // 税额 = 商品或服务金额 - (商品或服务金额 / (1 + 税率))
        temp.taxAmount = this.roundFractional(
          val.saleAmount - val.saleAmount / (1 + val.taxRate / 100)
        )
        // 应税金额 = 商品或服务金额 - 税额
        temp.taxableAmount = this.roundFractional(
          val.saleAmount - temp.taxAmount
        )
        // 价税合计 = 商品或服务金额
        temp.totalAmount = val.saleAmount
      } else {
        // 税额 = 商品或服务金额 * 税率
        temp.taxAmount = this.roundFractional(
          val.saleAmount * (val.taxRate / 100)
        )
        // 应税金额 = 商品或服务金额
        temp.taxableAmount = val.saleAmount
        // 价税合计 = 应税金额 + 税额
        temp.totalAmount = this.roundFractional(
          temp.taxAmount + temp.taxableAmount
        )
      }
      return temp
    },
    getSum(key = 'totalAmount') {
      this.form[key] = this.roundFractional(
        this.table.rows.reduce((add, item) => {
          return (add += item[key] ? item[key] * 1 : 0)
        }, 0)
      )
    },
    clickLock(event, name) {
      if (name === 'remark') {
        if (this.remarkLock) {
          this.remarkLock = false
          event.target.innerText = '锁定'
        } else {
          this.remarkLock = true
          event.target.innerText = '解锁'
        }
      }
      if (name === 'summary') {
        if (this.summaryLock) {
          this.summaryLock = false
          event.target.innerText = '锁定'
        } else {
          this.summaryLock = true
          event.target.innerText = '解锁'
        }
      }
    },
    handleTdyslxDmChange(val) {
      // 清空特定业务信息
      this.tdywColumns = []
      this.tdywName = ''
      this.form.invoiceSpecList = []
      this.getTdywInfo()
      if (val !== '') {
        const tdywRows = []
        if (val === '03') {
          tdywRows.push({
            rowState: true,
            gzfwFsd: '',
            gzfwXxdz: '',
            gzfwXmmc: '',
            gzfwXmbh: '',
            gzfwKsbz: 0,
            gzfwKqybybh: ''
          })
        } else if (val === '04') {
          tdywRows.push({
            hwysQyd: '',
            hwysDdd: '',
            hwysYsgj: '',
            hwysYsgjth: '',
            hwysYshwmc: ''
          })
        } else if (val === '05') {
          this.table.rows.forEach((row) => {
            tdywRows.push({
              rowState: true,
              bdcxsCqzh: '',
              bdcxsDqdz: '',
              bdcxsXxdz: '',
              bdcxsHtbh: '',
              bdcxsXmbh: '',
              bdcxsKqbz: 0,
              bdcxsMjdw: ''
            })
          })
        } else if (val === '06') {
          this.table.rows.forEach((row) => {
            tdywRows.push({
              rowState: true,
              bdcSf: '',
              bdcXxdz: '',
              bdcQsrq: '',
              bdcJzrq: '',
              bdcKsbz: 0,
              bdcCqjh: '',
              bdcPjdw: ''
            })
          })
        } else if (val === '09') {
          tdywRows.push({
            lkysCxr: '',
            lkysCxrzjlx: '',
            lkysCxrzjhm: '',
            lkysCxrq: '',
            lkysCfd: '',
            lkysDdd: '',
            lkysJtgjlx: '',
            lkysDj: ''
          })
        }
        this.form.invoiceSpecList = tdywRows
      }
    },
    getTdywInfo() {
      const lxdm = this.form.tdyslxDm || ''
      if (lxdm !== '') {
        const tdyw = this.tdywOptions.find(item => item.code === lxdm)
        this.tdywName = tdyw?.text
        if (lxdm === '03') {
          this.tdywColumns = this.jzfwColumns
        } else if (lxdm === '04') {
          this.tdywColumns = this.hwysColumns
        } else if (lxdm === '05') {
          this.tdywColumns = this.bdcxsColumns
        } else if (lxdm === '06') {
          this.tdywColumns = this.bdczlColumns
        } else if (lxdm === '09') {
          this.tdywColumns = this.lkysColumns
        }
      }
      this.form.invoiceSpecList = this.form.invoiceSpecList || []
    },
    handleAddSpec() {
      this.form.invoiceSpecList.push({
        rowState: true,
        lkysCxrq: '',
        gzfwKsbz: 0,
        bdcxsKqbz: 0,
        bdcKsbz: 0
      })
    },
    /* 删除特定业务明细 */
    handleDeleteSpec(row, index) {
      this.form.invoiceSpecList.splice(index, 1)
    },
    handleRowState(row) {
      row.rowState = !row.rowState
      if (this.form.tdyslxDm === '05') {
        row.bdcxsDqdz = ''
      } else if (this.form.tdyslxDm === '06') {
        row.bdcSf = ''
      } else {
        row.gzfwFsd = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__row {
  .el-input--mini .el-input__inner {
    border: 0;
    padding: 0 0 0 3px;
  }
}

::v-deep .el-table--mini td {
  padding: 0;
}

.dialog-box {
  height: calc(100vh - 200px) !important;
}

.form-title {
  width: 85%;
  height: 40px;
  line-height: 40px;
  position: fixed;
  top: 4vh;
  text-align: center;

  span {
    font-size: 20px;
  }
}

.sh_1 {
  ::v-deep .el-form-item {
    margin-bottom: 2px !important;
  }
}

.partition-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin: 0 0;

  .partition-left,
  .partition-right {
    width: 49%;

    .partition-title {
      font-size: 16px;
      color: #409eff;
      margin-bottom: 8px;
    }
  }
}

.table-box {
  width: 100%;
  margin: 0px 0 5px 0;

  ::v-deep .el-table .cell {
    padding: 0 2px !important;
  }

  ::v-deep .el-table__body {
    border-collapse: collapse;
    // 表格里面的单元格加深颜色
    //tbody td {
    //  border: 1px solid #a9a9a9;
    //}
  }

  ::v-deep .el-date-editor {
    padding-left: 30px;
  }

  .table-head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;

    .table-title {
      font-size: 16px;
      color: #409eff;

      .table-btn {
        ::v-deep .el-button {
          margin: 5px 15px;
        }
      }
    }
  }

  .table-footer {
    display: flex;
    justify-content: space-between;
    margin: 10px 5px 10px 5px;

    .footer-box {
      display: flex;
      align-items: center;

      span {
        &:first-child {
          margin-right: 5px;
        }

        &:last-child {
          // color: #0080ff;
          display: inline-block;
          line-height: 25px;
          min-width: 100px;
          height: 25px;
          border-bottom: 1px solid #0080ff;
        }
      }
    }
  }
}

.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}

.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}

::v-deep .alingRight {
  input {
    text-align: right !important;
  }
}

::v-deep .alingRightRed {
  input {
    text-align: right !important;
    color: red !important;
  }
}

.red {
  color: red;
}

::v-deep .red-status .el-table__footer-wrapper tbody td {
  // color: red;
  // 排除第7列（单价）和第10列（税率）
  &:not(:nth-child(7)):not(:nth-child(10)) {
    color: red;
  }
}

.charge-item {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
}

.lockSpan {
  color: #409eff;
  font-size: 12px;
  cursor: pointer;
  padding-right: 15px;
}

.tdywTitle {
  width: 50%;
  height: 40px;
}

.rowSf {
  width: calc(100% - 40px) !important;
}

.rowSfSpan {
  cursor: pointer;
  color: #1890ff;
  line-height: 27px;
  width: 40px;
}
</style>
