import request from '@/utils/request'

/**
 * 创建、编辑账套时获取初始化信息
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
export function voucherAddInit (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/add/init',
    method: 'get',
    params: data
  })
}

/**
 * 获取摘要
 * @param {*} data { booksId: '' }
 * @returns
 */
export function getModelSummaryList (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/getModelSummaryList',
    method: 'post',
    params: data
  })
}

/**
 * 获取会计科目
 * @param {*} data { booksId: '' }
 * @returns
 */
export function getModelItemList (data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getModelItemList',
    method: 'get',
    params: data
  })
}

/**
 * 获取结算方式
 * @param {*} data { booksId: '' }
 * @returns
 */
export function cashiersettlementtype (data) {
  return request({
    url: '/financial/books/basedata/cashiersettlementtype/getModelList',
    method: 'post',
    params: data
  })
}

/**
 * 获取凭证信息
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
export function loadVoucher (params) {
  return request({
    url: '/financial/accounting/accountingVoucher/loadVoucher',
    method: 'get',
    params: params
  })
}

/**
 * 根据入账信息生成凭证类容
 * @param {*} data { booksId: '', perriod:'' ,id:'行id',mergitem:'合并' }
 * @returns
 */
export function generateVoucher (data) {
  return request({
    url: '/financial/asset/assetPosting/generateVoucher',
    method: 'post',
    params: data
  })
}

/**
 * 获取摘要列表数据
 * @param {*} data { booksId: '' }
 * @returns
 */
export function loadSummaryList (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/page',
    method: 'post',
    data: data
  })
}

/**
 * 获取单条摘要信息
 * @param {*} data 摘要id
 * @returns
 */
export function loadSummaryInfo (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/load/' + data,
    method: 'get'
  })
}

/**
 * 保存单条摘要信息
 * @param {*} data 摘要信息
 * @returns
 */
export function saveSummaryInfo (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/save',
    method: 'post',
    params: data
  })
}

/**
 * 删除单条摘要信息
 * @param {*} data 摘要id
 * @returns
 */
export function removeSummaryInfo (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/delete',
    method: 'post',
    params: data
  })
}
/**
 * 批量删除摘要信息
 * @param {*} data 摘要id
 * @returns
 */
export function removeBatchRemoveSummaryInfo (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/delete',
    method: 'post',
    params: data
  })
}

/**
 * 获取会计科目的辅助核算类型列表
 * @param {*} data { booksId: '', accountingItemId:  }
 * @returns
 */
export function getAssistForVoucher (params) {
  return request({
    url: '/financial/books/basedata/assistType/getAssistForVoucher',
    method: 'get',
    params: params
  })
}

/**
 * 获取常用凭证列表
 * @param {*} data
 * @returns
 */
export function mAccountingVoucher (data) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/page',
    method: 'post',
    data: data
  })
}

/**
 * 导入时根据id获取常用凭证信息
 * @param {*} data
 * @returns
 */
export function getMAccountingVoucher (data) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/getVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 根据id获取常用凭证信息(编辑、查看)
 * @param {*} data
 * @returns
 */
export function loadMAccountingVoucher (id) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/load/' + id,
    method: 'get'
  })
}

/**
 * 保存常用凭证信息
 * @param {*} params { booksId: '' }
 * @param {*} data { id: '', sortNumber: '', voucherName: '' }
 * @returns
 */
export function saveMAccountingVoucher (params, data) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/saveVoucher',
    method: 'post',
    params: params,
    data: data,
    payload: true
  })
}

/**
 * 删除常用凭证信息
 * @param {*} data { id: '', booksId: '' }
 * @returns
 */
export function deleteMAccountingVoucher (data) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/deleteVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 保存凭证
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function saveVoucher (params, data) {
  return request({
    url: '/financial/accounting/accountingVoucher/saveVoucher',
    method: 'post',
    params: params,
    data: data,
    payload: true
  })
}

/**
 * 获取会计科目的期间余额
 * @param {*} params { booksId: '', period: '' }
 * @returns
 */
export function getPeriodBalance (params) {
  return request({
    url: '/financial/accounting/accountingItemBalance/getPeriodBalance',
    method: 'get',
    params: params
  })
}

/**
 * 获取会计科目的期间实际发生额
 * @param {*} params { booksId: '', period: '' }
 * @returns
 */
export function getPeriodPnl (params) {
  return request({
    url: '/financial/accounting/accountingItemBalance/getPeriodPnl',
    method: 'get',
    params: params
  })
}

/**
 * 获取会计科目的期间余额
 * @param {*} params { booksId: '', period: '' }
 * @returns
 */
export function pageVoucherShow (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/pageVoucherShow',
    method: 'post',
    data: data
  })
}

/**
 * 凭证导入保存
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function saveImportTemp (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/saveImportTemp',
    method: 'post',
    data: data
  })
}

/**
 * 原始凭证上传保存
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function saveOriginalVoucher (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/saveOriginalVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 凭证号重排手动调整列表
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function manualCheckList (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/manualCheck',
    method: 'get',
    params: data
  })
}

/**
 * 凭证号重排检测调整
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function voucherCheck (params) {
  return request({
    url: `/financial/accounting/accountingVoucher/check`,
    method: 'get',
    params: params
  })
}

/**
 * 凭证号重排自动整理列表返回
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function analysis ({ booksId, period, startCode = '', sortType }) {
  return request({
    url: `/financial/accounting/accountingVoucher/analysis?booksId=${booksId}&period=${period}&startCode=${startCode}&sortType=${sortType}`,
    method: 'post',
    payload: true
  })
}

/**
 * 凭证号重排手动调整列表返回
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function manualAnalysis ({ booksId, period, startCode = '', data }) {
  return request({
    url: `/financial/accounting/accountingVoucher/manualAnalysis?booksId=${booksId}&period=${period}&startCode=${startCode}`,
    method: 'post',
    data: data,
    payload: true
  })
}

/**
 * 凭证号重排保存
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function reorganize ({ booksId, period, data }) {
  return request({
    url: `/financial/accounting/accountingVoucher/reorganize?booksId=${booksId}&period=${period}`,
    method: 'post',
    data: data,
    payload: true
  })
}

/**
 * 删除凭证
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function deleteVoucher (params) {
  return request({
    url: `/financial/accounting/accountingVoucher/deleteVoucher`,
    method: 'post',
    params: params
    // payload: true
  })
}

/**
 * 凭证冲销
 */
export function loadWriteOff (params) {
  return request({
    url: `/financial/accounting/accountingVoucher/loadWriteOff`,
    method: 'get',
    params: params
  })
}

/**
 * 检测冲销状态
 */
export function checkWriteOff (data) {
  return request({
    url: `/financial/accounting/accountingVoucher/checkWriteOff`,
    method: 'post',
    data: data
  })
}

/**
 * 结转损益
 */
export function carryoverVoucher (params) {
  return request({
    url: `/financial/accounting/accountingCarryover/generateVoucher`,
    method: 'get',
    params: params
  })
}

/**
 * 自定义结转损益
 */
export function customCarryoverVoucher (params) {
  return request({
    url: `/financial/accounting/accountingCustomCarryover/generateVoucher`,
    method: 'get',
    params: params
  })
}

/**
 * 会计凭证来源
 */
export function accountingLoadView (id) {
  return request({
    url: `/financial/accounting/accountingBusinessRelation/loadView/${id}`,
    method: 'get'
  })
}

/**
 * 出纳凭证来源
 */
export function cashierLoadView (data) {
  return request({
    url: '/financial/cashier/business/loadView',
    data,
    method: 'post'
  })
}

/**
 * 出纳凭证来源,获取code
 */
export function cashierLoadViewCode (id) {
  return request({
    url: '/financial/cashier/cashierBusinessRelation/loadView/' + id,
    method: 'get'
  })
}

/**
 * 编辑会计凭证时初始化页面
 */
export function voucherEditInit (data) {
  return request({
    url: `/financial/accounting/accountingVoucher/edit/init`,
    method: 'get',
    params: data
  })
}

/**
 * 会计凭证生成固定资产卡片
 */
export function generateAssetCard (params, data) {
  return request({
    url: '/financial/accounting/accountingVoucher/generateAssetCard',
    method: 'post',
    params,
    data,
    payload: true
  })
}

/**
 * 复制会计凭证
 */
export function voucherCopy (params, data) {
  return request({
    url: '/financial/accounting/accountingVoucher/copyVoucher',
    method: 'post',
    params,
    data,
    payload: true
  })
}

/**
 * 保存所有摘要信息
 * @param {*} data 摘要信息
 * @returns
 */
export function batchSave (params, data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/batchSave',
    method: 'post',
    params,
    data,
    payload: true
  })
}

/**
 * 检查用户是否拥有指定机构的权限
 * @param {*} data 
 * @returns 
 */
export function checkAuthOrg (data) {
  return request({
    url: `/financial/accounting/accountingVoucher/checkAuthOrg`,
    method: 'get',
    params: data
  })
}
/**
 * 分页获取分组列表
 * @param {
    page（分页参数）
    rows（分页参数）
    bookId （账套id）
    accountingPeriodId（期间id）
    name（分组名称）
    remark（备注）} data 摘要信息
 * @returns
 */
export function groupPage (data) {
  return request({
    url: '/financial/accounting/accountingVoucherGroup/page',
    method: 'post',
    data,
  })
}

/**
 * 查看关联分组
 * @param {*} data 
 * @returns 
 */
export function loadGroup (id) {
  return request({
    url: `/financial/accounting/accountingVoucherGroup/load/${id}`,
    method: 'get',
  })
}
/**
 * 新增、编辑关联分组
 * @param {
    id（数据id，新增时为空）
      booksId（账套id）
    accountingPeriodId（期间id）
    name（分组名称）
    remark（备注）} data 摘要信息
 * @returns
 */
export function saveGroup (data) {
  return request({
    url: '/financial/accounting/accountingVoucherGroup/save',
    method: 'post',
    data,
  })
}
/**
 * 删除关联分组
 * @param {
    booksId（账套id）
    accountingPeriodId（期间id）
    ids（数据id，多个用英文逗号隔开）} data 摘要信息
 * @returns
 */
export function deleteGroup (data) {
  return request({
    url: '/financial/accounting/accountingVoucherGroup/deleteX',
    method: 'post',
    data,
  })
}
/**
 * 获取该分组已关联的凭证列表（分页，默认100条，每页最大条数500条）
 * @param {
    page（分页参数）
    rows（分页参数）
    booksId（账套id）
    accountingPeriodId（期间id）
    groupId（分组id）
    excludeVoucherId（需要排除的会计凭证id）} data 摘要信息
 * @returns
 */
export function getRelatedVoucherPage (data) {
  return request({
    url: '/financial/accounting/accountingVoucherGroup/getRelatedVoucherPage',
    method: 'post',
    data,
  })
}
/**
 * 删除该分组已关联的凭证列表（分页，默认100条，每页最大条数500条）
 * @param {
    booksId（账套id）
    accountingPeriodId（期间id）
    groupId（分组id）
    voucherIds（会计凭证id， 多个用英文逗号隔开）} data 摘要信息
 * @returns
 */
export function deleteRelatedVoucher (data) {
  return request({
    url: '/financial/accounting/accountingVoucherGroup/deleteRelatedVoucher',
    method: 'post',
    data,
  })
}

/**
 * 获取可以关联的凭证列表 (分页，默认20条，每页最大条数500条)
 * @param {
    page（分页参数）
    rows（分页参数）
    booksId（账套id）
    accountingPeriodId（期间id）
    bussinessDateStart（开始日期）
    bussinessDateEnd（结束日期）
    voucherName（凭证字号）
    accountingItemId（会计科目id）} data 摘要信息
 * @returns
 */
export function voucherPage (data) {
  return request({
    url: '/financial/accounting/accountingVoucherGroup/voucherPage',
    method: 'post',
    data,
  })
}
/**
 * 保存关联凭证
 * @param {
    booksId（账套id）	  
    accountingPeriodId（期间id）
    groupId（分组id）
    voucherIds（会计凭证id， 多个用英文逗号隔开）} data 摘要信息
    
 * @returns
 */
export function saveRelateVoucher (data) {
  return request({
    url: '/financial/accounting/accountingVoucherGroup/saveRelateVoucher',
    method: 'post',
    data,
  })
}