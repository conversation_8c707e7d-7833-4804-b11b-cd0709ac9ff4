<template>
  <div>
    <gever-dialog
      v-loading="dialogLoading"
      destroy-on-close
      :visible.sync="dialogVisible"
      :title="$t(`${title}开票`)"
      :buttons="dialogButtons"
      append-to-body
      width="85%"
      @close="closeDialog"
    >
      <el-form
        ref="_batchReceiptFormRef"
        v-loading="loading"
        :model="Form"
        :rules="rules"
        label-width="110px"
        class="bill-batch-receipt"
      >
        <el-form-item :label="$t('机构名称')" prop="orgName" style="width: 100%">
          <GeverInput
            v-model="Form.orgName"
            disabled
          />
        </el-form-item>
        <el-form-item :label="$t('库存票据')" prop="billName">
          <gever-select
            v-model="Form.billName"
            :options="billStockOptions"
            label-prop="showBillName"
            @optionClick="getKuCunData"
            @change="changeBillName"
          />
        </el-form-item>
        <el-form-item :label="$t('开票张数')" prop="receiptSize">
          <gever-input-number
            v-model="Form.receiptSize"
            :controls="false"
            :precision="0"
            :min="0"
            :max="receiptMaxSize"
            :disabled="!Form.billName || Form.billName === '' || receiptSizeDisable === true"
            class="alingRight"
            @blur="receiptSizeBlur"
          ></gever-input-number>
        </el-form-item>
        <el-form-item :label="$t('票据号码')" prop="">
          <GeverInput
            v-model="Form.beginBillNo"
            disabled
            :placeholder="$t('自动带出')"
          />
        </el-form-item>
        <el-form-item :label="$t('至')" class="center-label" prop="">
          <GeverInput
            v-model="Form.endBillNo"
            disabled
            :placeholder="$t('自动带出')"
          />
        </el-form-item>
        <el-form-item :label="$t('开票人')" prop="">
          <GeverInput v-model="Form.drawer" disabled />
        </el-form-item>
        <el-form-item :label="$t('开票日期')" prop="drawDate">
          <el-date-picker
            v-model="Form.drawDate"
            style="width: 100%"
            type="date"
            :placeholder="$t('选择日期')"
          />
        </el-form-item>
        <el-form-item :label="$t('收款人')" prop="payee">
          <el-autocomplete
            v-model.trim="Form.payee"
            :title="Form.payee"
            :fetch-suggestions="
              (queryString, callback) =>
                getCashierCommonInfo(queryString, callback, 'payee')
            "
            :placeholder="$t('请输入收款人')"
            style="width: 100%"
          >
            <template #default="{ item }">
              <el-row type="flex" justify="space-between" :title="item.name">
                <span class="overflow-hidden">{{ item.name }}</span>
                <span>
                  <el-button
                    type="name"
                    @click.stop="handleRemoveInfo(item.id, 'payee')"
                  >
                    {{ $t('删除') }}
                  </el-button>
                </span>
              </el-row>
            </template>
            <template #append>
              <el-button @click="handleSaveInfo(Form.payee, 'payee')">
                <span class="blue">
                  {{ $t('保存') }}
                </span>
              </el-button>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item :label="$t('开票金额')" prop="">
          <gever-input
            v-model="Form.totalAmount"
            style="width: 100% !important"
            :controls="false"
            :precision="2"
            :placeholder="$t('自动计算')"
            class="alingRight"
            disabled
          />
        </el-form-item>
        <el-form-item :label="$t('缴款人/单位')" prop="otherSide" style="width: 100%">
          <gever-input
            v-model.trim="Form.otherSide"
            type="textarea"
            :rows="3"
            @blur="otherSideBlur"
          />
        </el-form-item>
        <el-form-item :label="$t('票据状态')" prop="invalidState">
          <gever-select
            v-model="Form.invalidState"
            :options="invalidStateOptions"
            :clearable="false"
            numberKey
            @change="invalidStateChange"
          />
        </el-form-item>
        <el-form-item :label="$t('作废原因')" prop="invalidReason" v-if="Form.invalidState === 1">
          <gever-select
            v-model="Form.invalidReason"
            :options="invalidReasonList"
            :clearable="false"
            :placeholder="$t('请选择作废原因')"
          />
        </el-form-item>
        <el-form-item :label="$t('作废备注')" prop="invalidRemark" v-if="Form.invalidState === 1" style="width: 100%">
          <GeverInput
            v-model="Form.invalidRemark"
            maxlength="200"
            type="textarea"
            :rows="2"
            style="width: 100%"
            :placeholder="$t('请输入备注')"
          />
        </el-form-item>
        <el-form-item prop="remark" style="width: 100%">
          <template slot="label">
            <div style="line-height: 20px; margin-top: 2px">
              <span style="font-size: 14px">附注</span>
              <span v-if="['batchAdd'].includes(pageFlag)">
                <br />
              </span>
              <span
                v-if="['batchAdd'].includes(pageFlag)"
                class="lockSpan"
                @click="clickLock($event, 'remark')"
              >
                锁定
              </span>
            </div>
          </template>
          <gever-input
            v-model="Form.remark"
            type="textarea"
            :rows="2"
            maxlength="200"
            :placeholder="$t('请输入')"
          />
        </el-form-item>
        <el-form-item prop="summary" style="width: 100%">
          <template slot="label">
            <div style="line-height: 20px; margin-top: 2px">
              <span style="font-size: 14px">摘要</span>
              <span v-if="['batchAdd'].includes(pageFlag)">
                <br />
              </span>
              <span
                v-if="['batchAdd'].includes(pageFlag)"
                class="lockSpan"
                @click="clickLock($event, 'summary')"
              >
                锁定
              </span>
            </div>
          </template>
          <el-autocomplete
            v-model.trim="Form.summary"
            type="textarea"
            :rows="2"
            :maxlength="1000"
            :title="Form.summary"
            :fetch-suggestions="
              (queryString, callback) =>
                getCashierCommonInfo(queryString, callback, 'summary')
            "
            :placeholder="$t('请输入摘要')"
            style="width: 100%"
          >
            <template #default="{ item }">
              <el-row type="flex" justify="space-between" :title="item.summary">
                <span class="overflow-hidden">{{ item.summary }}</span>
              </el-row>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item></el-form-item>
      </el-form>
      <el-row type="flex" justify="space-between">
        <div class="gever-title">
          {{ $t('收款明细') }}
        </div>
        <div>
          <el-button
            type="default"
            round
            :disabled="table.rows.length >= detailSize"
            @click="handleAddTableItem"
          >
            {{ $t('添加') }}
          </el-button>
        </div>
      </el-row>
      <!-- 表格 -->
      <gever-table
        ref="_geverTableRef"
        :columns="tableColumns"
        :data="table.rows"
        highlight-current-row
        height="250px"
        :loading="loading"
        :pagination="false"
      >
        <!-- 收入类型 -->
        <template #incomeType="{ row, index }">
          <el-cascader
            :ref="`_cascaderRef${index}`"
            v-model="row.incomeType"
            :show-all-levels="false"
            :options="handleOptionsCascader(row.incomeType)"
            :props="{
              expandTrigger: 'hover',
              checkStrictly: true,
              value: 'id',
              label: 'text'
            }"
          ></el-cascader>
        </template>
        <!-- 收款项目 -->
        <template #project="{ row, index }">
          <el-autocomplete
            v-model.trim="row.project"
            :title="row.project"
            :maxlength="58"
            :fetch-suggestions="
              (queryString, callback) => getProjectInfo(queryString, callback)
            "
            style="width: 100%"
            @blur="handleBlur(row, index)"
            @select="handleBlur(row, index)"
          >
            <template #default="{ item }">
              <el-row type="flex" justify="space-between" :title="item.name">
                <span class="overflow-hidden">{{ item.name }}</span>
                <span>
                  <el-button
                    type="name"
                    @click.stop="handleRemoveInfo(item.id, 'project')"
                  >
                    {{ $t('删除') }}
                  </el-button>
                </span>
              </el-row>
            </template>
            <template #append>
              <el-button
                @click="projectInfoSave(row.project)"
              >
                <span class="blue">
                  {{ $t('保存') }}
                </span>
              </el-button>
            </template>
          </el-autocomplete>
        </template>
        <!-- 金额 -->
        <template #amount="{ row }">
          <gever-input
            v-model="row.amount"
            :controls="false"
            :precision="2"
            class="alingRight"
            @blur="inputBlur"
          />
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <gever-input
            v-model="row.remark"
            type="textarea"
            maxlength="50"
            @change="handleRowRemarkChange"
          />
        </template>
        <template #operation="{ row, index }">
          <el-button
            type="text"
            @click="handleDeleteItem(row, index)"
          >
            {{ $t('删除') }}
          </el-button>
        </template>
      </gever-table>
    </gever-dialog>
    <IncomeChoose
      :visible.sync="incomeVisible"
      :is-type-list="true"
      @confirm="handleSelectIncome"
    />
  </div>
</template>

<script>
import { formatDate } from '@/utils/date.js'
import {
  combotree,
  deleteInfo,
  saveInfo
} from '@/api/financial/cashier/voucher'
import IncomeChoose from '../../../../financial/income-without-contract/income/components/IncomeChoose.vue'
import _ from 'lodash'
import {
  billDetailBatchReceipt,
  billCommonInfoPayeeList,
  billCommonInfoPayeeSave,
  billCommonInfoPayeeDelete,
  billProjectDelete,
  billCommonInfoChargeItemList,
  billCommonInfoChargeItemSave,
  getHistorySummary,
  seleteList
} from '@/api/sundryBill/sundryBillDetail.js'
import { getIncomeType } from '@/api/financial/bills/billUseDetail.js'
import { load } from '@/api/sundryBill/sundryBillType.js'
import { mapState } from 'vuex'
import { Big } from 'big.js'

export default {
  components: {
    IncomeChoose
  },
  props: {
    orgParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      remarkLock: false,
      summaryLock: false,
      loading: false,
      dialogLoading: false,
      dialogVisible: false,
      incomeVisible: false,
      receiptMaxSize: 0,
      billStockOptions: [],
      incomeTypeOptions: [
        { id: 2, text: '非合同收入' }
      ],
      dialogButtons: [
        {
          type: '',
          text: '取 消',
          buttonStatus: true,
          callback: () => {
            this.dialogVisible = false
          }
        },
        {
          type: 'primary',
          text: '保存&返回',
          buttonStatus: true,
          callback: this.saveForm
        }
      ],
      title: '',
      detailSize: 3,
      table: {
        rows: []
      },
      Form: {
        operator: this.$store.state.user.name,
        invalidState: 0,
        invalidReason: '',
        invalidRemark: '',
        billName: '',
        receiptSize: '',
        beginBillNo: '',
        endBillNo: '',
        beginBillNumber: 0,
        endBillNumber: 0,
        incomeType: '2',
        incomeSourceId: '',
        invoicingStage: 2,
        otherSide: '',
        valueCascader: ['2']
      },
      receiptSizeDisable: false,
      KuCunData: {},
      pageFlag: 'batchAdd',
      rules: {
        drawDate: [
          {
            required: true,
            message: '该选择开票日期',
            trigger: ['blur', 'change']
          }
        ],
        otherSide: [
          {
            required: true,
            message: '请输入缴款人/单位',
            trigger: ['blur', 'change']
          }
        ],
        payee: [
          {
            required: true,
            trigger: ['blur', 'change'],
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('该输入收款人'))
              }
              if (value.length > 50) {
                callback(new Error('收款人长度不能超过50'))
              }
              callback()
            }
          }
        ],
        billName: [
          { required: true, message: '请选择库存', trigger: ['blur', 'change'] }
        ],
        receiptSize: [
          { required: true, message: '请输入开票张数', trigger: ['blur', 'change'] }
        ],
        valueCascader: [
          {
            required: true,
            message: '请选择收入类型',
            trigger: ['blur', 'change']
          }
        ],
        invalidState: [
          { required: true, message: this.$t('请选择票据状态'), trigger: 'change' }
        ],
        invalidReason: [
          { required: true, message: this.$t('请选择作废原因'), trigger: 'change' }
        ]
      },
      optionsCascader: [],
      invalidStateOptions: [
        {
          id: '0',
          text: '开出'
        },
        {
          id: '1',
          text: '作废'
        }
      ],
      invalidReasonList: [
        {
          id: '1',
          text: '填写错误'
        },
        {
          id: '2',
          text: '遗失'
        },
        {
          id: '3',
          text: '其他'
        }
      ]
    }
  },
  computed: {
    ...mapState('area', ['areaLogin']), // 地区机构数据
    ...mapState('financial', ['books', 'booksId']),
    tableColumns() {
      return [
        {
          prop: 'incomeType',
          headerAlign: 'center',
          align: 'left',
          minWidth: 120,
          label: '收入类型 *'
        },
        {
          prop: 'project',
          headerAlign: 'center',
          minWidth: 350,
          label: '收款项目 *',
          align: 'left'
        },
        {
          prop: 'amount',
          headerAlign: 'center',
          minWidth: 100,
          label: '金额 *'
        },
        {
          prop: 'remark',
          headerAlign: 'center',
          minWidth: 180,
          label: '备注'
        },
        { label: '操作', prop: 'operation', width: '170', fixed: 'right' }
      ]
    },
    comma() {
      return (val, keep_dec = 2) => {
        if (val) {
          if (keep_dec) {
            val = Number(val).toFixed(keep_dec)
          }
          val = String(val)
          if (val.indexOf('.') === -1) {
            return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
          }
          return (
            val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
            '.' +
            val.split('.')[1]
          )
        } else {
          return '0.00'
        }
      }
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getIncomeTypeFun()
  },
  methods: {
    handleOptionsCascader(incomeType) {
      let optionsCascader = _.cloneDeep(this.optionsCascader)
      if (optionsCascader.length && incomeType && !incomeType.includes('1')) {
        optionsCascader = optionsCascader.map((cur) => {
          cur.disabled = cur.id === '1'
          return cur
        })
      }
      return optionsCascader
    },
    getSeleteList() {
      seleteList({
        orgId: this.areaLogin.id
      }).then((res) => {
        this.Form.valueCascader = ['2']
        this.billStockOptions = res.data.map((cur) => {
          cur.showBillName = `${cur.billAbbr}(${cur.billCode}${cur.beginNo}-${cur.billCode}${cur.endNo})`
          return cur
        })
        if (this.billStockOptions.length === 1) {
          this.getKuCunData(this.billStockOptions[0])
        }
      })
    },
    getBillCommonInfoPayeeList() {
      const params = {
        type: 'payee',
        orgId: this.areaLogin?.id
      }
      billCommonInfoPayeeList(params).then((res) => {
        if (res.data.length > 0) {
          this.$set(this.Form, 'payee', res.data[0].name)
        }
      })
    },
    getIncomeTypeFun() {
      getIncomeType().then((res) => {
        this.optionsCascader = res.data.filter((item) => item.attributesType > 1)
      })
    },
    /* 选择非合同收入 */
    handleSelectIncome(data) {
      this.Form.incomeSourceId = data.id
    },
    async getProjectInfo(queryString, callback) {
      const { data } = await billCommonInfoChargeItemList({
        booksId: '',
        orgId: this.areaLogin?.id
      })
      const result = queryString
        ? data.filter(
            ({ text = '', name = '' }) => name.indexOf(queryString) > -1
          )
        : data
      result.forEach((data) => {
        data.value = data.name || ''
      })
      callback(result)
    },
    async getCashierCommonInfo(queryString, callback, type) {
      let url = combotree
      let params = {
        booksId: '',
        type,
        orgId: this.areaLogin?.id
      }
      if (type === 'payee') {
        url = billCommonInfoPayeeList
      }
      if (type === 'summary') {
        url = getHistorySummary
        params = {
          orgId: this.areaLogin?.id,
          summary: this.Form.summary
        }
      }

      const { data } = await url(params)
      const result = queryString
        ? data.filter(({ text = '', name = '', summary = '' }) =>
            type === 'payee'
              ? name.indexOf(queryString) > -1
              : type === 'summary'
              ? summary.indexOf(queryString) > -1
              : text.indexOf(queryString) > -1
          )
        : data
      result.forEach((data) => {
        data.value = data.text
        if (type === 'payee') {
          data.value = data.name
        }
        if (type === 'summary') {
          data.value = data.summary
        }
      })
      callback(result)
    },
    handleRemoveInfo(infoId, type) {
      this.$confirm('确认删除吗', this.$t('提示'), {
        confirmButtonText: '确认',
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(async (action) => {
          try {
            if (type === 'payee') {
              const id = infoId
              await billCommonInfoPayeeDelete({ id })
            }
            if (type === 'project') {
              await billProjectDelete({ id: infoId })
            } else {
              await deleteInfo({ infoId, type, booksId: '' })
            }
          } catch (error) {
            console.log(error)
          }
        })
        .catch(() => {})
    },
    async projectInfoSave(text) {
      try {
        if (text) {
          const params = {}
          params.orgId = this.areaLogin?.id
          params.name = text
          const { message } = await billCommonInfoChargeItemSave(params)
          this.$message.success(message)
        }
      } catch (error) {
        console.log(error)
      }
    },
    async handleSaveInfo(text, type) {
      try {
        if (text) {
          const params = {}
          params[type] = text
          params.booksId = ''
          params.orgId = this.areaLogin?.id
          params.tableName = type
          params.name = text

          let url = saveInfo
          if (type === 'payee') {
            url = billCommonInfoPayeeSave
          }

          const { message } = await url(params)
          this.$message.success(message)
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 保存提交
    saveForm() {
      this.$refs['_batchReceiptFormRef'].validate(async (valid) => {
        if (valid) {
          if (this.Form.receiptSize <= 0) {
            return this.$message.warning(this.$t('请输入开票张数大于0'))
          }
          const tableList = []
          this.table.rows.forEach((cur) => {
            if (cur.amount || cur.project || cur.remark) {
              tableList.push(cur)
            }
          })
          // 开出作废的票缴款人填一个无即可，如果填了多个缴款人，缴款人数量也要与开票数量一致
          const otherSideArr = this.Form.otherSide.replaceAll('\n', ',').split(',')
          if (this.Form.invalidState === 1) {
            if (otherSideArr.length > 1 && otherSideArr.length !== this.Form.receiptSize) {
              return this.$message.warning(this.$t('开票张数和缴款人/单位数量不一致，直接开出作废的票缴款人/单位填写“无”或数量与开票张数一致'))
            }
          } else {
            if (this.Form.receiptSize !== otherSideArr.length) {
              return this.$message.warning(this.$t('开票张数和缴款人/单位数量不一致，需调整后重新保存'))
            }
            if (!tableList.length) {
              return this.$message.warning(this.$t('请完善收款明细'))
            }
          }
          if (tableList.length > this.detailSize) {
            return this.$message.warning(this.$t('收款明细数量不能大于' + this.detailSize))
          }
          tableList.forEach((l, index) => {
            if (l.incomeType.length < 2 || l.incomeType[1] === '') {
              this.$message.warning(
                this.$t(
                  `第 ${index + 1} 行 收入类型请选择非合同收入及明细项`
                )
              )
              throw new Error('非合同收入类型明细项为空')
            }
            if (!l?.project) {
              this.$message.warning(
                this.$t(`第 ${index + 1} 行 收款项目 不能为空`)
              )
              throw new Error('收款项目不能为空')
            }
            if (Number(l?.amount?.replaceAll(',', '') || 0) <= 0) {
              this.$message.warning(
                this.$t(`第 ${index + 1} 行 金额 不能小于等于0！`)
              )
              throw new Error('金额不能小于等于零')
            }
          })
          const entityJsonString = {
            ...this.Form,
            incomeType: this.Form.valueCascader[0],
            incomeSourceId:
              this.Form.valueCascader.length > 1
                ? this.Form.valueCascader[1]
                : '',
            summary: this.Form?.summary
              ? this.Form?.summary
              : tableList?.[0]?.project,
            invalidReason: this.Form.invalidState === 1 ? this.Form.invalidReason : '',
            invalidRemark: this.Form.invalidState === 1 ? this.Form.invalidRemark : ''
          }
          if (entityJsonString.drawDate) {
            entityJsonString.drawDate = formatDate(
              entityJsonString.drawDate,
              'yyyy-MM-DD'
            )
          }
          entityJsonString.totalAmount = Number(
            entityJsonString?.totalAmount?.replaceAll(',', '') || 0
          )
          entityJsonString.otherSide = otherSideArr.join(',')
          const itemsJsonString = tableList.map((cur) => {
            if (cur.incomeType.length > 1) {
              cur.incomeSourceId = cur.incomeType[1]
            } else {
              cur.incomeSourceId = ''
            }
            cur.incomeType = cur.incomeType[0]
            if (cur.incomeType === '2') {
              cur.incomeNonContractTypeId = cur.contractCollectionId
              delete cur.contractCollectionId
            }
            if (cur.incomeType === '1') {
              delete cur.unitPrice
              delete cur.quantity
            }
            cur.amount = Number(cur?.amount?.replaceAll(',', '') || 0)
            return cur
          })
          entityJsonString.orgId = this.areaLogin.id

          const params = {
            entityJsonString: JSON.stringify(entityJsonString),
            itemsJsonString: JSON.stringify(itemsJsonString)
          }
          try {
            this.dialogLoading = true
            const res = await billDetailBatchReceipt(params)
            if (res?.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.dialogLoading = false
              this.closeDialog()
            } else {
              this.dialogLoading = false
              this.$message.error(this.$t(res.message))
            }
          } catch (error) {
            this.dialogLoading = false
          }
        }
      })
    },
    // 获取子组件的数据
    getKuCunData(row) {
      this.KuCunData = { ...row }
      this.detailSize = row.detailSize
      this.Form = {
        ...this.Form,
        billName: row.billAbbr,
        receiptSize: '',
        beginBillNo: '',
        endBillNo: '',
        beginBillNumber: 0,
        endBillNumber: 0,
        drawer: this.$store.state.user.name,
        businessCode: '',
        businessId: '',
        billTypeId: row.billTypeId,
        billCode: row.billCode,
        batchNo: row.batchNo
      }
      if (!this.Form.drawDate) {
        this.$set(this.Form, 'drawDate', new Date())
      }
      this.receiptMaxSize = row.copies
      // 打开页面时，如果还未打印的，明细行自动补足三行
      if (this.Form.billName) {
        if (this.pageFlag === 'batchAdd') {
          const rowsLength = this.table.rows.length || 0
          for (let i = rowsLength; i < 3; i++) {
            this.table.rows.push({ amount: '', project: '', remark: '', incomeType: ['2'] })
          }
        }
      }
      this.$nextTick(() => {
        if (this.Form.billName && this.Form.otherSide) {
          this.Form.receiptSize = this.Form.otherSide.split(',').length
          this.receiptSizeDisable = true
        } else {
          this.Form.receiptSize = 0
        }
        // 根据自动计算开票张数
        this.receiptSizeBlur()
      })
    },
    // 添加表格项
    handleAddTableItem() {
      if (!this.Form.billName) {
        return this.$message.warning(this.$t('请先选择库存'))
      }
      const item = {
        amount: '',
        project: '',
        remark: '',
        incomeType: ['2']
      }
      this.table.rows.push(item)
      this.setTotalAmount()
    },
    // 删除表格项
    handleDeleteItem(row, index) {
      this.table.rows.splice(index, 1)
      this.setTotalAmount()
      this.handleRowRemarkChange()
      this.handleSummaryChange()
    },
    closeDialog() {
      this.table.rows = []
      this.Form = {
        operator: this.$store.state.user.name,
        invalidState: 0,
        invalidReason: '',
        invalidRemark: '',
        billName: '',
        receiptSize: '',
        beginBillNo: '',
        endBillNo: '',
        beginBillNumber: 0,
        endBillNumber: 0,
        incomeType: '2',
        incomeSourceId: '',
        invoicingStage: 2,
        otherSide: '',
        valueCascader: ['2']
      }
      this.dialogVisible = false
      this.remarkLock = false
      this.summaryLock = false
      this.$emit('updateTable')
    },
    async show(flag, row, title) {
      if (flag) {
        this.pageFlag = flag
        this.$set(this.Form, 'drawDate', new Date())
        this.$set(this.Form, 'drawer', this.$store.state.user.name)
        this.$set(this.Form, 'orgName', this.areaLogin.fname)
        this.Form.invoicingStage = 2
        this.title = title
      }
      this.dialogVisible = true
      this.getSeleteList()
      this.getBillCommonInfoPayeeList()
    },
    subFloatToInt(arg1, arg2) {
      var r1, r2, m, n
      try {
        r1 = arg1.toString().split('.')[1].length
      } catch (e) {
        r1 = 0
      }
      try {
        r2 = arg2.toString().split('.')[1].length
      } catch (e) {
        r2 = 0
      }
      m = Math.pow(10, Math.max(r1, r2))
      // 动态控制精度长度
      n = r1 >= r2 ? r1 : r2
      return ((arg1 * m - arg2 * m) / m).toFixed(n)
    },
    // 金额失焦
    inputBlur(e, precision = 2) {
      if (e.target.value === '') return this.setTotalAmount()
      const reg =
        /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/
      const value = e.target.value.replaceAll(',', '') * 1
      if (reg.test(value) || value > 0) {
        e.target.value = this.comma(value * 1)
      } else {
        e.target.value = (0 * 1).toFixed(precision)
      }
      this.setTotalAmount()
    },
    handleBlur(row, index) {
      this.handleSummaryChange()
    },
    // 开票张数
    receiptSizeBlur() {
      if (this.Form.receiptSize > 0) {
        this.Form.beginBillNumber = new Big(this.KuCunData.beginSeq)
        this.Form.endBillNumber = this.Form.beginBillNumber.add(new Big(this.Form.receiptSize - 1))
        load(this.Form.billTypeId).then((res) => {
          const numberLength = Number(res.data.numberLength) + 1
          this.Form.beginBillNo = this.zeroFill(this.Form.beginBillNumber, numberLength)
          this.Form.endBillNo = this.zeroFill(this.Form.endBillNumber, numberLength)
        })
      } else {
        this.Form.beginBillNumber = 0
        this.Form.beginBillNo = ''
        this.Form.endBillNumber = 0
        this.Form.endBillNo = ''
      }
    },
    // 补零操作
    zeroFill(num, length) {
      const len = num.toString().length
      const diff = length - len
      if (diff > 0) {
        return Array(diff).join('0') + num
      }
    },
    setTotalAmount() {
      const tableList = this.table.rows
      let total = 0
      tableList.forEach((l) => {
        total += l.amount.replaceAll(',', '') * 1
      })
      if (!isNaN(total)) {
        this.$set(
          this.Form,
          'totalAmount',
          this.comma(total)
        )
      } else {
        this.$set(this.Form, 'totalAmount', '')
      }
    },
    handleRowRemarkChange() {
      if (!this.remarkLock) {
        let remarkStr = ''
        this.table.rows.forEach((item) => {
          const rowRemark = item.remark
          if (rowRemark) {
            if (!remarkStr.includes(rowRemark)) {
              if (remarkStr !== '') {
                remarkStr = remarkStr + '，' + rowRemark
              } else {
                remarkStr = rowRemark
              }
            }
          }
        })
        this.$set(this.Form, 'remark', remarkStr)
      }
    },
    handleSummaryChange() {
      if (this.table.rows[0] && !this.summaryLock) {
        this.$set(this.Form, 'summary', this.table.rows[0].project)
      }
    },
    clickLock(event, name) {
      if (name === 'remark') {
        if (this.remarkLock) {
          this.remarkLock = false
          event.target.innerText = '锁定'
        } else {
          this.remarkLock = true
          event.target.innerText = '解锁'
        }
      }
      if (name === 'summary') {
        if (this.summaryLock) {
          this.summaryLock = false
          event.target.innerText = '锁定'
        } else {
          this.summaryLock = true
          event.target.innerText = '解锁'
        }
      }
    },
    changeBillName() {
      if (this.Form.billName === '') {
        this.Form.receiptSize = 0
        this.receiptSizeBlur()
        this.receiptSizeDisable = false
      }
    },
    otherSideBlur() {
      let otherStr = this.Form.otherSide.trim().replaceAll('，', ',')
        .replaceAll('；', ',')
        .replaceAll(';', ',')
        .replaceAll('/', ',')
        .replaceAll('.', ',')
        .replaceAll('。', ',')
        .replaceAll('、', ',')
      if (otherStr.startsWith(',')) {
        otherStr = otherStr.substring(1, otherStr.length)
      }
      if (otherStr.endsWith(',')) {
        otherStr = otherStr.substring(0, otherStr.length - 1)
      }
      this.Form.otherSide = otherStr
      if (otherStr.length > 0 && this.Form.billName) {
        this.Form.receiptSize = otherStr.split(',').length
        this.receiptSizeDisable = true
      } else {
        this.Form.receiptSize = 0
        this.receiptSizeDisable = false
      }
      // 根据自动计算开票张数
      this.receiptSizeBlur()
    },
    invalidStateChange(val) {
      if (val === 1) {
        this.Form.invalidReason = ''
        this.Form.invalidRemark = ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .bill-batch-receipt {
  display: flex;
  flex-wrap: wrap;
  .el-form-item {
    width: 50%;
    padding-right: 10px;
    .el-form-item__content > .el-select {
      width: 100%;
    }
  }
  .block {
    width: 100%;
  }
}

::v-deep .alingRight {
  .el-input__inner {
    text-align: right;
    // width: 100px;
  }
}

::v-deep .center-label .el-form-item__label {
  text-align: center !important;
}

.clear {
  color: #909399;
}

.blue {
  color: blue !important;
}

::v-deep .el-dialog .el-dialog__body {
  max-height: 70vh;
}

::v-deep .el-cascader {
  width: 100%;
}

.lockSpan {
  color: #409eff;
  font-size: 12px;
  cursor: pointer;
}
</style>
