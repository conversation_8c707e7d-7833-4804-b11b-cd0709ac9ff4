<template>
  <div>
    <fold-box :right-title="$t('注册订购')">
      <template #right>
        <div class="right-box">
          <div class="btnHead">
            <el-button
              v-hasPermi="`financial.invoice.invoiceRegistry.registry`"
              icon="el-icon-plus"
              type="primary"
              round
              :loading="loading"
              @click="handleDG()"
            >
              {{ '订 购' }}
            </el-button>
          </div>
          <div
            v-for="item in formList"
            :key="item.registryType"
            class="registryList"
            :style="total === 1 ? { width: '99%' } : {}"
          >
            <table class="cTable">
              <tr>
                <td class="rTitle">集体经济组织名称：</td>
                <td>{{ item.orgName }}</td>
              </tr>
              <tr>
                <td class="rTitle">发票服务商名称：</td>
                <td>
                  {{ item.registryTypeName }}
                  <span
                    v-if="item.registryFlag === 1"
                    class="accountBtn"
                    @click="viewAccount(item)"
                  >
                    查看收款账户
                  </span>
                </td>
              </tr>
              <tr>
                <td class="rTitle">订购状态：</td>
                <td>{{ item.registryFlag !== 1 ? '未订购' : '已订购' }}</td>
              </tr>
              <tr>
                <td class="rTitle">有效期间：</td>
                <td>{{ item.validPeriod }}</td>
              </tr>
            </table>
            <div class="btnGroup">
              <el-button
                v-if="
                  hasPermission('financial.invoice.invoiceRegistry.renew') &&
                    item.registryFlag === 1 &&
                    (item.validBeginDate || '') !== '' &&
                    (item.validEndDate || '') !== ''
                "
                type="default"
                icon="el-icon-s-operation"
                round
                :disabled="item.registryFlag !== 1"
                :loading="loading"
                @click="handleXY(item)"
              >
                {{ '续 约' }}
              </el-button>
              <el-button
                v-if="
                  hasPermission('financial.invoice.invoiceRegistry.update') &&
                    (item.id || '') !== ''
                "
                type="default"
                icon="el-icon-document-checked"
                round
                :disabled="(item.id || '') === ''"
                :loading="loading"
                @click="handleGX(item)"
              >
                {{ '更新信息' }}
              </el-button>
              <el-button
                v-if="
                  hasPermission('financial.invoice.invoiceRegistry.record') &&
                    item.registryFlag === 1
                "
                type="default"
                icon="el-icon-document"
                round
                :disabled="item.registryFlag !== 1"
                :loading="loading"
                @click="handleJL(item)"
              >
                {{ '订购记录' }}
              </el-button>
              <el-button
                v-if="
                  hasPermission('financial.invoice.invoiceRegistry.delete') &&
                    (item.registryFlag || 0) === 0 &&
                    (item.id || '') !== ''
                "
                type="default"
                icon="el-icon-delete"
                round
                :disabled="item.registryFlag === 1 || (item.id || '') === ''"
                :loading="loading"
                @click="handleSC(item)"
              >
                {{ '删 除' }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </fold-box>
    <details-dialog
      v-if="detailsVisible"
      :dialog-visible.sync="detailsVisible"
      :area-login="areaLogin"
      :status-type="statusType"
      :form-data="form"
      @updateList="getRegistry"
    ></details-dialog>
    <!-- 订购续约记录 -->
    <gever-dialog
      title="订购/续约记录"
      width="65%"
      :visible.sync="registryLogVisible"
      destroy-on-close
      style="margin-top: 5vh"
      :append-to-body="false"
      @close="registryLogClose"
    >
      <gever-table
        ref="_registryLogRef"
        :columns="registryLogColumns"
        :data="registryLogs"
        :loading="registryLogLoading"
        :pagination="false"
        :height="460"
      >
        <template #logType="{ row }">
          {{ row.logType === 1 ? '续约' : '注册订购' }}
        </template>
        <template #logState="{ row }">
          {{
            row.logState === 1 ? '申请中' : row.logState === 2 ? '已审批' : ''
          }}
        </template>
        <template #options="{ row }">
          <el-button
            type="text"
            :disabled="(form.registryFlag === 1 && row.logType === 0) || row.logState === 2"
            @click="deleteRegistryLog(row)"
          >
            {{ '删除' }}
          </el-button>
        </template>
      </gever-table>
    </gever-dialog>
    <!-- 查看收款账户 -->
    <gever-dialog
      title="收款账户"
      width="40%"
      :visible.sync="accountVisible"
      destroy-on-close
      style="margin-top: 8vh"
      :append-to-body="false"
      @close="accountClose"
    >
      <div class="account-info">
        <div class="info-item">
          <span class="label">户 名：</span>
          <span class="value">{{ applyInfo.applierAccountName }}</span>
        </div>
        <div class="info-item">
          <span class="label">账 号：</span>
          <span class="value">{{ applyInfo.applierAccountNo }}</span>
        </div>
        <div class="info-item">
          <span class="label">开 户 行：</span>
          <span class="value">{{ applyInfo.applierBankName }}</span>
        </div>
        <div class="info-item">
          <span class="label">联 系 人：</span>
          <span class="value">{{ applyInfo.applierContact }}</span>
        </div>
        <div class="info-item">
          <span class="label">服务金额：</span>
          <span class="value">{{ applyInfo.applierAmount }}</span>
        </div>
      </div>
    </gever-dialog>
  </div>
</template>

<script>
import {
  initLoad,
  deleteByIds,
  loadLogs,
  deleteRegistryLog
} from '@/api/financial/invoice/invoiceRegistry.js'
import detailsDialog from './components/details.vue'
import { createNamespacedHelpers } from 'vuex'

const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'InvoiceRegistry',
  components: { detailsDialog },
  data() {
    return {
      loading: false,
      formList: [],
      total: 0,
      form: {},
      detailsVisible: false,
      statusType: 'view',
      registryLogVisible: false,
      registryLogLoading: false,
      registryLogFromId: '',
      registryLogs: [],
      registryLogColumns: [
        {
          label: '序号',
          type: 'index',
          width: 55,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '服务商',
          prop: 'registryType',
          minWidth: '80',
          align: 'center'
        },
        { label: '类型', prop: 'logType', minWidth: '70', align: 'center' },
        { label: '状态', prop: 'logState', minWidth: '60', align: 'center' },
        {
          label: '申请开始日期',
          prop: 'beginDate',
          minWidth: '100',
          align: 'center'
        },
        {
          label: '申请结束日期',
          prop: 'endDate',
          minWidth: '100',
          align: 'center'
        },
        {
          label: '申请日期',
          prop: 'applyDate',
          minWidth: '100',
          align: 'center'
        },
        { label: '申请人', prop: 'applier', minWidth: '80', align: 'center' },
        { label: '操作', prop: 'options', minWidth: '60', align: 'center' }
      ],
      accountVisible: false,
      applyInfo: {}
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler() {
        this.getRegistry()
      }
    }
  },
  mounted() {},
  methods: {
    getRegistry() {
      if (this.areaLogin.level !== 5) {
        return this.$message.warning('请选择机构')
      }
      this.form = {}
      this.loading = true
      initLoad(this.areaLogin.id)
        .then((res) => {
          this.total = res.data.total
          const returnRows = res.data.rows
          returnRows.forEach((item) => {
            if ((item.orgName || '') === '') {
              item.orgName = this.areaLogin.fname
            }
          })
          this.formList = returnRows
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 判断是否有权限
    hasPermission(value) {
      const operationCodes = JSON.parse(
        localStorage.getItem('userInfo')
      ).operationCodes
      return operationCodes.indexOf(value) >= 0
    },
    // 订购
    handleDG() {
      this.statusType = 'add'
      this.detailsVisible = true
    },
    // 续约
    handleXY(item) {
      if (!item.registryFlag || item.registryFlag !== 1) {
        return this.$message.warning('该机构还未订购发票')
      }
      this.form = item
      this.statusType = 'renew'
      this.detailsVisible = true
    },
    // 更细信息
    handleGX(item) {
      this.form = item
      this.statusType = 'edit'
      this.detailsVisible = true
    },
    // 删除
    handleSC(item) {
      if (item.registryFlag === 1) {
        return this.$message.warning(
          '服务商' + item.registryTypeName || '' + '已注册订购，不能删除'
        )
      }
      this.$confirm(
        '是否确定删除该服务商（' + item.registryTypeName + '）的记录？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.loading = true
        deleteByIds({ id: item.id })
          .then((res) => {
            if (res.returnCode === '0') {
              this.$message.success(res.message)
              this.getRegistry()
            } else {
              this.$message.error(res.message)
            }
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 订购/续约记录
    handleJL(item) {
      this.form = item
      this.registryLogVisible = true
      this.registryLogFromId = item.id
      this.loadJL()
    },
    loadJL() {
      this.registryLogLoading = true
      loadLogs(this.registryLogFromId)
        .then((res) => {
          this.registryLogs = res.data
        })
        .finally(() => {
          this.registryLogLoading = false
        })
    },
    // 关闭订购/续约记录
    registryLogClose() {
      this.form = {}
      this.registryLogs = []
      this.registryLogFromId = ''
      this.registryLogVisible = false
      this.getRegistry()
    },
    // 查看收款账户
    viewAccount(item) {
      this.applyInfo = {
        applierAccountName: item.applierAccountName || '',
        applierAccountNo: item.applierAccountNo || '',
        applierBankName: item.applierBankName || '',
        applierContact: item.applierContact || '',
        applierAmount: item.applierAmount || ''
      }
      this.accountVisible = true
    },
    deleteRegistryLog(item) {
      if (item.logState === 2) {
        return this.$message.warning('该记录已审批通过，不能删除')
      }
      this.$confirm('是否确定删除该记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteRegistryLog({ id: item.id })
          .then((res) => {
            if (res.returnCode === '0') {
              this.$message.success(res.message)
              this.loadJL()
            } else {
              this.$message.error(res.message)
            }
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 关闭查看收款账户
    accountClose() {
      this.applyInfo = {}
      this.accountVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.btnHead {
  width: 99%;
  height: 40px;
  text-align: right;
  clear: both;

  ::v-deep .el-button--mini.is-round {
    padding: 10px 15px;
    font-size: 15px !important;
  }
}

.registryList {
  width: calc(50% - 1px);
  padding: 5px 5px;
  float: left;
}

.registryList:not(:last-child) {
  border-right: 1px solid #c8c8c8;
}

.cTable {
  width: calc(100% - 12px);
  margin: 0 auto;
  border-collapse: collapse;

  tr:not(:last-child) {
    border-bottom: 1px solid #ebeef5;
  }

  td {
    font-size: 18px;
    line-height: 1.5;
    padding: 20px 5px;
  }

  .rTitle {
    width: 185px;
    color: #409eff;
    font-size: 18px;
    font-weight: bold;
    text-align: right;
  }
}

.btnGroup {
  margin: 20px 0 20px 0;
  width: calc(100% - 12px);
  padding: 10px 10px;
  text-align: center;

  ::v-deep .el-button--mini.is-round {
    padding: 10px 15px;
    font-size: 15px !important;
  }
}

.accountBtn {
  font-size: 14px;
  color: #409eff;
  margin-left: 25px;
  cursor: pointer;
}

.account-info {
  padding: 20px;

  .info-item {
    margin-bottom: 15px;
    font-size: 16px;
    line-height: 1.5;

    .label {
      color: #409eff;
      font-weight: bold;
      margin-right: 10px;
      text-align: right;
      display: inline-block;
      width: 100px;
    }

    .value {
      color: #333;
    }
  }
}
</style>
