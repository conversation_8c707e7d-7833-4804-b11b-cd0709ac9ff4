<template>
  <div>
    <gever-dialog
      :visible.sync="dialogVisible"
      title="查看关联的资产管理信息"
      width="90%"
      perch-height="380"
      @close="closeDiglog"
    >
      <div class="right-box">
        <el-form ref="_searchFormRef" :model="queryParams" inline>
          <el-form-item prop="assetName" :label="$t('资产名称')">
            <gever-input v-model="queryParams.assetName" class="w150" />
          </el-form-item>
          <el-form-item prop="assetCode" :label="$t('资产编号')">
            <gever-input v-model="queryParams.assetCode" class="w150" />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              round
              plain
              icon="el-icon-search"
              @click="handleSearch"
            >
              {{ $t('搜索') }}
            </el-button>
          </el-form-item>
        </el-form>
        <gever-table
          ref="_tableRef"
          height="400"
          :columns="assetManagementColumnsTable"
          :page-sizes="[20, 50, 100, 200, 500, 800, 1000]"
          :data="table.rows"
          :total="table.total"
          :pagi="queryParams"
          :loading="loading"
          @pagination-change="getList"
        ></gever-table>
      </div>
    </gever-dialog>
  </div>
</template>

<script>
import { assetManagementColumnsTable } from '../config'
import {
  getResourceDockList
} from '@/api/financial/asset/assetResourceDock'
export default {
    name: 'AssetManagementInfo',
    props: {
        assetManagementInfo: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            dialogVisible: false,
            loading: false,
            assetManagementColumnsTable,
            table: {
                total: 0,
                rows: []
            },
            queryParams: {
                page: 1,
                rows: 20,
                orgId: '',
                orgCode: '',
                booksId: '',
                assetName: '',
                assetCode: '',
                assetUsage: '',
                assetPurpose: '',
                assetSituation: '',
                assetCategory: '',
                dockState: ''
            }
        }
    },
    watch: {
        dialogVisible() {
            if (this.dialogVisible) {
                this.getList()
            }
        }
    },
    methods: {
        closeDiglog() {
            this.dialogVisible = false
            this.table = { rows: [], total: 0 }
            this.queryParams = {
                page: 1,
                rows: 20,
                orgId: '',
                orgCode: '',
                booksId: '',
                assetName: '',
                assetCode: '',
                assetUsage: '',
                assetPurpose: '',
                assetSituation: '',
                assetCategory: '',
                dockState: ''
            }
        },
        async getList() {
            try {
                this.loading = true
                this.queryParams.booksId = this.assetManagementInfo.booksId
                this.queryParams.orgId = this.assetManagementInfo.orgId
                this.queryParams.orgCode = this.assetManagementInfo.orgCode
                this.queryParams.assetTemplate = 'ALL'
                this.queryParams.assetCategory = 'J'
                this.queryParams.ids = this.assetManagementInfo.assetResourceId ? this.assetManagementInfo.assetResourceId.split(',').filter(Boolean) : []
                console.log(this.queryParams, 'queryParams')
                const res = await getResourceDockList(this.queryParams)
                if (res && res.returnCode === '0') {
                    this.table = res.data
                } else {
                    this.table = { rows: [], total: 0 }
                }
            } catch (error) {
                console.log(error)
                this.table = { rows: [], total: 0 }
                this.loading = false
            } finally {
                this.loading = false
            }
        },
        handleSearch() {
            this.queryParams.page = 1
            this.getList()
        }
    }
}
</script>

<style>

</style>
