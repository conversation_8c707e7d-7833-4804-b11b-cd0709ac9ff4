<!--
 * @Description:账套管理
 * @version:
 * @Author: 未知
 * @Date: 2021-10-25 15:35:20
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-06-20 11:32:41
-->

<template>
  <div>
    <fold-box left-title="账套信息" right-title="账套维护">
      <template #left>
        <el-form label-width="100px" label-suffix=":">
          <el-form-item label="账套名称">
            {{ bookInfo.booksName }}
          </el-form-item>
          <el-form-item label="账套年份">
            {{ bookInfo.createYear }}
          </el-form-item>
          <el-form-item label="会计期间">
            {{ bookInfo.acccountingPeriodName }}
          </el-form-item>
          <el-form-item label="出纳期间">
            {{ bookInfo.cashierPeriodName }}
          </el-form-item>
          <el-form-item label="资产期间">
            {{ bookInfo.assetPeriodName }}
          </el-form-item>
          <el-form-item label="年结状态">
            {{ yearEndedText(bookInfo.yearEnded) }}
          </el-form-item>
        </el-form>
      </template>
      <template #right>
        <div class="right-box">
          <el-form ref="_searchFormRef" :model="queryParams" inline>
            <el-form-item prop="booksName" :label="$t('账套名称')">
              <gever-input v-model="queryParams.booksName" />
            </el-form-item>
            <el-form-item prop="createYear" :label="$t('年份')">
              <gever-select
                ref="_selectRef"
                v-model="queryParams.createYear"
                clearable
                url="/financial/books/listBooksYear"
                label-prop="createYear"
                value-prop="createYear"
                method="get"
                class="w100"
              />
            </el-form-item>
            <el-form-item prop="useable" :label="$t('启用状态')">
              <gever-select
                v-model="queryParams.useable"
                :options="optionsMap.useable"
                clearable
                class="w100"
              />
            </el-form-item>
            <el-form-item prop="yearEnded" :label="$t('年结状态')">
              <gever-select
                v-model="queryParams.yearEnded"
                :options="optionsMap.yearEnded"
                :clearable="false"
                class="w100"
              />
            </el-form-item>
            <el-form-item prop="yearEnded" :label="$t('地区')">
              <el-select
                v-model="queryParams.areaName"
                class="w100%"
                :title="queryParams.areaName"
              >
                <el-option
                  :value="treeNodeAreaName"
                  style="height: auto; padding: 0"
                >
                  <area-tree
                    :is-lazy-load="true"
                    :tree-data="treeData"
                    :expanded-nodes="expandedNodes"
                    @loadChildNode="loadChildNode"
                    @selectedNodeChange="handleNodeChange"
                  />
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="booksTypeId" :label="$t('账套类型')">
              <gever-select
                ref="_selectRef"
                v-model="queryParams.booksTypeId"
                clearable
                url="/financial/bookTypes/listBooksType"
                label-prop="title"
                value-prop="id"
                method="get"
                class="w100%"
              />
            </el-form-item>
            <el-form-item prop="orgType" :label="$t('机构类型')">
              <el-select
                  v-model="queryParams.orgType"
                  clearable
                  class="w150"
                >
                  <el-option
                    v-for="item in orgTypeOptions"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                  />
                </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                round
                plain
                icon="el-icon-search"
                @click="handleSearch"
              >
                {{ $t('搜索') }}
              </el-button>

              <!-- <el-button
                type="primary"
                icon="el-icon-refresh-right"
                circle
                plain
                @click="handleRefresh"
              /> -->
            </el-form-item>
          </el-form>

          <el-row class="table-toolbar">
            <el-button
              v-hasPermi="'financial.bookManage.books.add'"
              round
              icon="el-icon-plus"
              type="primary"
              @click="handleAdd"
            >
              {{ $t('新增') }}
            </el-button>
            <el-button
              v-hasPermi="'financial.bookManage.books.delete'"
              round
              plain
              icon="el-icon-delete"
              @click="handleRemove"
            >
              {{ $t('删除') }}
            </el-button>
            <el-button
              v-hasPermi="'financial.bookManage.books.enable'"
              round
              plain
              type="primary"
              icon="el-icon-check"
              @click="handleStartUsing"
            >
              {{ $t('启用') }}
            </el-button>
            <el-button
              v-hasPermi="'financial.bookManage.books.disable'"
              round
              plain
              icon="el-icon-close"
              @click="handleStopUsing"
            >
              {{ $t('停用') }}
            </el-button>

            <el-button
              v-hasPermi="'financial.bookManage.books.yearEndedBaseData'"
              :loading="yearEndedLoading"
              round
              plain
              type="primary"
              icon="el-icon-upload"
              @click="handleYearEndedBaseData"
            >
              {{ $t('结转基础数据') }}
            </el-button>

            <el-button
              v-hasPermi="'financial.bookManage.books.initType'"
              round
              plain
              type="primary"
              icon="el-icon-refresh-left"
              @click="handleInitialize"
            >
              {{ $t('重置基础数据') }}
            </el-button>

            <el-button
              v-if="userId === '1'"
              :loading="generateLoading"
              round
              plain
              type="primary"
              icon="el-icon-plus"
              @click="handleGenerateBooks"
            >
              {{ $t('批量生成预设账套') }}
            </el-button>
            <el-button
              v-hasPermi="'financial.bookManage.books.batchSaveBooks'"
              round
              plain
              type="primary"
              icon="el-icon-plus"
              @click="handleGenerateAccount"
            >
              {{ $t('批量建账') }}
            </el-button>
            <el-button
              v-hasPermi="'financial.bookManage.books.export'"
              round
              plain
              type="primary"
              icon="el-icon-download"
              @click="handleExportTemp"
            >
              {{ $t('导出') }}
            </el-button>
            <el-button
              v-hasPermi="'financial.bookManage.books.midYearClosure'"
              round
              plain
              type="primary"
              icon="el-icon-circle-close"
              @click="handleMidYearClosure"
            >
              {{ $t('年中停账') }}
            </el-button>
          </el-row>

          <gever-table
            ref="_tableRef"
            :loading="loading"
            :data="table.rows"
            :total="table.total"
            :columns="columns"
            :options-map="optionsMap"
            :pagi="queryParams"
            @row-click="getBookInformation"
            @pagination-change="getList"
          >
            <template #midYearClosurePeriod="{ row }">
              {{ row.midYearClosurePeriod === 0 ? '未停账' : row.midYearClosurePeriod }}
            </template>
            <template #operation="{ row }">
              <gever-button-group
                :buttons="[
                  {
                    text: $t('查看'),
                    click: 'view',
                    permission: 'financial.bookManage.books.view'
                  },
                  {
                    text: '编辑',
                    click: 'edit',
                    permission: 'financial.bookManage.books.edit'
                  },
                  {
                    text: '删除',
                    click: 'remove',
                    permission: 'financial.bookManage.books.delete'
                  }
                ]"
                @view="handleView(row)"
                @edit="handleEdit(row)"
                @remove="handleRemove(row)"
              />
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <public-drawer
      title="账套维护"
      :visible.sync="visible"
      @close="$refs['_formRef'].resetFields()"
    >
      <el-form
        ref="_formRef"
        label-width="160px"
        :model="form"
        :rules="rules"
        :show-message="false"
        :disabled="formDisabled"
      >
        <div class="gever-title">{{ $t('基本信息') }}</div>
        <el-form-item v-show="false" prop="orgName" />
        <el-form-item prop="orgId" label="所属机构" required>
          <gever-input
            :value="form.orgName"
            placeholder="请点击按钮选择"
            readonly
            :disabled="isEdit"
          >
            <template #append>
              <el-button :disabled="isEdit" @click="orgSelectorVisible = true">
                {{ $t('机构') }}
              </el-button>
            </template>
          </gever-input>
          <public-drawer
            title="选择机构"
            :visible.sync="orgSelectorVisible"
            :size="20"
            :buttons="[
              {
                buttonStatus: true,
                text: '确定',
                type: 'primary',
                callback: 'choose'
              }
            ]"
            @choose="handleChooseOrganization"
          >
            <area-org-selector
              expand-first
              @current-change="handleCurrentChange"
            />
          </public-drawer>
        </el-form-item>
        <el-form-item prop="booksTypeId" label="账套类型" required>
          <gever-select
            v-model="form.booksTypeId"
            placeholder="机构选择后获取账套类型"
            label-prop="title"
            :options="booksTypeOptions"
            :number-key="false"
            :disabled="isEdit"
            @change="handleBooksTypeChange"
          />
        </el-form-item>
        <el-form-item prop="booksName" label="账套名称">
          <gever-input
            v-model="form.booksName"
            placeholder="请输入"
            :maxlength="60"
            show-word-limit
          />
        </el-form-item>
        <el-form-item prop="booksCode" label="账套编号" :show-message="true">
          <gever-input
            v-model="form.booksCode"
            placeholder="编号只能填写数字和英文组合"
            :maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item prop="beginPeriod" label="开始期间数" required>
          <gever-select
            v-model="form.beginPeriod"
            :number-key="false"
            :disabled="isEdit"
            :options="periodOptions"
          />
        </el-form-item>
        <el-form-item prop="createYear" label="建账年份" required>
          <gever-select
            v-model="form.createYear"
            url="/financial/books/listYear"
            value-prop="createYear"
            label-prop="createYear"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item prop="createTime" label="创建时间" required>
          <el-date-picker
            v-model="form.createTime"
            type="datetime"
            disabled
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('选择日期时间')"
          />
        </el-form-item>
        <el-row>
          <el-col :span="6" :offset="0">
            <el-form-item prop="useable" label="启用">
              <el-switch
                v-model="form.useable"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="18" :offset="0">
            <el-form-item>
              <el-checkbox
                v-model="form.accountingUseable"
                :true-label="1"
                :false-label="0"
              >
                {{ $t('会计核算') }}
              </el-checkbox>
              <el-checkbox
                v-model="form.cashierUseable"
                :true-label="1"
                :false-label="0"
              >
                {{ $t('出纳管理') }}
              </el-checkbox>
              <el-checkbox
                v-model="form.assetUseable"
                :true-label="1"
                :false-label="0"
              >
                {{ $t('固定资产') }}
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item prop="remark" label="说明">
          <gever-input
            v-model="form.remark"
            :rows="3"
            type="textarea"
            resize="none"
            :maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <div class="gever-title">{{ $t('科目长度') }}</div>
        <el-form-item
          :prop="`settingList.${getIndexByCode('060')}.value`"
          label="会计科目最大级别"
          required
        >
          <gever-select
            v-model="form.settingList[getIndexByCode('060')].value"
            :options="
              new Array(10)
                .fill(0)
                .map((item, index) => ({ id: index + 1, text: index + 1 }))
            "
          />
        </el-form-item>
        <el-row type="flex" justify="start" align="center">
          <el-form-item label="会计科目编号长度" required />
          <div style="display: flex; flex-wrap: wrap">
            <template v-for="i in 10">
              <el-form-item
                v-if="
                  i <= Number(form.settingList[getIndexByCode('060')].value)
                "
                :key="i"
                :prop="`settingList.${getIndexByCode(`0${60 + i}`)}.value`"
                label-width="2px"
                required
              >
                <el-input-number
                  v-model="form.settingList[getIndexByCode(`0${60 + i}`)].value"
                  label=""
                  style="width: 60px"
                  :min="1"
                  :max="99"
                  :step="1"
                  :controls="false"
                />
              </el-form-item>
            </template>
          </div>
        </el-row>
        <el-form-item prop="orgId" label="注意">
          {{ $t('科目编号总长度必须小于等于127位') }}
        </el-form-item>

        <div class="gever-title">{{ $t('人员信息') }}</div>
        <el-form-item
          :prop="`settingList.${getIndexByCode('010')}.value`"
          label="单位名称"
        >
          <gever-input
            v-model="form.settingList[getIndexByCode('010')].value"
            show-word-limit
            :maxlength="60"
          />
        </el-form-item>
        <!-- <el-form-item
          :prop="`settingList.${getIndexByCode('001')}.value`"
          label="出纳员"
        >
          <gever-input
            v-model="form.settingList[getIndexByCode('001')].value"
            :maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          :prop="`settingList.${getIndexByCode('002')}.value`"
          label="会计员"
        >
          <gever-input
            v-model="form.settingList[getIndexByCode('002')].value"
            :maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          :prop="`settingList.${getIndexByCode('003')}.value`"
          label="资产管理员"
        >
          <gever-input
            v-model="form.settingList[getIndexByCode('003')].value"
            :maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          :prop="`settingList.${getIndexByCode('004')}.value`"
          label="制表人"
        >
          <gever-input
            v-model="form.settingList[getIndexByCode('004')].value"
            :maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          :prop="`settingList.${getIndexByCode('005')}.value`"
          label="单位负责人"
        >
          <gever-input
            v-model="form.settingList[getIndexByCode('005')].value"
            :maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          :prop="`settingList.${getIndexByCode('006')}.value`"
          label="监事会"
        >
          <gever-input
            v-model="form.settingList[getIndexByCode('006')].value"
            :maxlength="100"
            show-word-limit
          />
        </el-form-item> -->

        <div class="gever-title">{{ $t('出纳管理') }}</div>
        <el-form-item
          :prop="`settingList.${getIndexByCode('102')}.value`"
          label="出纳编号位数"
        >
          <el-input-number
            v-model="form.settingList[getIndexByCode('102')].value"
            :min="0"
            :step="1"
            :precision="0"
            :max="6"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12" :offset="0">
            <el-form-item
              :prop="`settingList.${getIndexByCode('110')}.value`"
              label="出纳是否关联会计"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('110')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              :prop="`settingList.${getIndexByCode('111')}.value`"
              label="启用银行对账"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('111')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item
          :prop="`settingList.${getIndexByCode('110')}.value`"
          label="出纳是否关联会计"
        >
          <el-switch
            v-model="form.settingList[getIndexByCode('110')].value"
            active-value="1"
            inactive-value="0"
          />
        </el-form-item> -->
        <!-- <el-form-item label="出纳是否关联票据">
          <el-switch
            v-model="form.isRelevanceBill"
            disabled
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item> -->
        <el-form-item
          :prop="`settingList.${getIndexByCode('202')}.value`"
          label="注意"
        >
          {{ $t('账套余额初始化关闭后不能设置出纳编号位数') }}
        </el-form-item>

        <div class="gever-title">{{ $t('会计核算') }}</div>
        <el-row :gutter="20">
          <el-col :span="12" :offset="0">
            <el-form-item
              :prop="`settingList.${getIndexByCode('202')}.value`"
              label="会计凭证编号位数"
              required
            >
              <el-input-number
                v-model="form.settingList[getIndexByCode('202')].value"
                :min="1"
                :max="6"
                :step="1"
                :precision="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              :prop="`settingList.${getIndexByCode('205')}.value`"
              label="是否不月结做账"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('205')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item :prop="`settingList.${getIndexByCode('202')}.value`" label="会计凭证编号位数" required>
          <el-input-number v-model="form.settingList[getIndexByCode('202')].value" :min="1" :max="6" :step="1" :precision="0" />
        </el-form-item> -->
        <el-row :gutter="20">
          <el-col :span="12" :offset="0">
            <el-form-item
              label-width="240px"
              :prop="`settingList.${getIndexByCode('203')}.value`"
              label="凭证是否只允许本人修改及删除"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('203')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              label-width="240px"
              :prop="`settingList.${getIndexByCode('204')}.value`"
              label="凭证分录科目是否显示余额"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('204')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              label-width="240px"
              :prop="`settingList.${getIndexByCode('417')}.value`"
              label="会计月结是否检查出纳对账一致"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('417')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              label-width="240px"
              :prop="`settingList.${getIndexByCode('418')}.value`"
              label="会计月结是否检查资产对账一致"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('418')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <!-- 固定资产增加参数 -->
          <el-col :span="24" :offset="0">
            <el-form-item
              label="应收款一级科目"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('220')}.value`"
            >
              <gever-select
                v-model="form.settingList[getIndexByCode('220')].value"
                filterable
                :options="accountingItemOptions"
                :disabled="!accountingItemOptions.length"
                label-prop="itemTitle"
                value-prop="itemCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item
              label="应付款一级科目"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('221')}.value`"
            >
              <gever-select
                v-model="form.settingList[getIndexByCode('221')].value"
                filterable
                :options="accountingItemOptions"
                :disabled="!accountingItemOptions.length"
                label-prop="itemTitle"
                value-prop="itemCode"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item
          :prop="`settingList.${getIndexByCode('202')}.value`"
          label="注意"
        >
          {{ $t('账套余额初始化关闭后不能设置会计凭证编号位数') }}
        </el-form-item>

        <div class="gever-title">{{ $t('固定资产') }}</div>
        <el-form-item
          :prop="`settingList.${getIndexByCode('502')}.value`"
          label="资产编号位数"
          required
        >
          <el-input-number
            v-model="form.settingList[getIndexByCode('502')].value"
            :min="1"
            :max="14"
            :step="1"
            :precision="0"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12" :offset="0">
            <el-form-item
              label="资产是否关联会计"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('510')}.value`"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('510')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              label="启用保存并入账按钮"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('511')}.value`"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('511')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              label="生成凭证时是否允许多借多贷"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('512')}.value`"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('512')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              label="是否每月计提折旧"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('513')}.value`"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('513')].value"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <!-- 固定资产增加参数 -->
          <el-col :span="24" :offset="0">
            <el-form-item
              label="固定资产一级科目"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('515')}.value`"
            >
              <!-- <el-switch v-model="form.settingList[getIndexByCode('515')].value" active-value="1" inactive-value="0" /> -->
              <gever-select
                v-model="form.settingList[getIndexByCode('515')].value"
                filterable
                :options="accountingItemOptions"
                label-prop="itemTitle"
                value-prop="itemCode"
                :disabled="!accountingItemOptions.length"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item
              label="累计折旧一级科目"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('516')}.value`"
            >
              <!-- <el-switch v-model="form.settingList[getIndexByCode('516')].value" active-value="1" inactive-value="0" /> -->
              <gever-select
                v-model="form.settingList[getIndexByCode('516')].value"
                filterable
                :options="accountingItemOptions"
                label-prop="itemTitle"
                value-prop="itemCode"
                :disabled="!accountingItemOptions.length"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item
              label="固定资产清理一级科目"
              label-width="220px"
              :prop="`settingList.${getIndexByCode('517')}.value`"
            >
              <gever-select
                v-model="form.settingList[getIndexByCode('517')].value"
                filterable
                :options="accountingItemOptions"
                label-prop="itemTitle"
                value-prop="itemCode"
                :disabled="!accountingItemOptions.length"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12" :offset="0">
            <el-form-item
              label="是否需要计提且当期到期的资产必须计提"
              label-width="300px"
              :prop="`settingList.${getIndexByCode('514')}.value`"
            >
              <el-switch
                v-model="form.settingList[getIndexByCode('514')].value"
                active-value="1"
                inactive-value="0"
                :disabled="disabled514"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="注意">
          {{ $t('录入资产信息后将不能设置资产编号位数') }}
        </el-form-item>
      </el-form>

      <template v-if="!formDisabled" #footer>
        <!-- :buttons="[
        {
          type: 'default',
          plain: true,
          text: '关闭',
          buttonStatus: !formDisabled,
          callback: () => (visible = false),
        },
        {
          type: 'primary',
          text: '保存',
          buttonStatus: !formDisabled,
          callback: handleSubmit,
        },
      ]" -->
        <el-button type="default" plain @click="visible = false">
          关闭
        </el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSubmit">
          保存
        </el-button>
      </template>
    </public-drawer>

    <el-dialog title="重置基础数据" :visible.sync="initVisible" width="30%">
      <el-checkbox
        v-for="item in initTypeList"
        :key="item.id"
        v-model="item.checked"
        class="mb10"
        :true-label="1"
        :false-label="0"
        :disabled="!!item.disabled"
      >
        {{ item.text }}
      </el-checkbox>

      <span slot="footer">
        <el-button @click="initVisible = false">取消</el-button>
        <el-button type="primary" @click="handleInitBooks">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="批量建账" :visible.sync="accountVisible" width="65%">
      <div
        v-loading="boxLoading"
        :element-loading-text="'数据保存中，请勿刷新页面，稍等几分钟...'"
      >
        <batchAccountCreation
          v-if="accountVisible"
          ref="batchref"
          :box-loading.sync="boxLoading"
          @close="accountClose"
        ></batchAccountCreation>
        <div
          style="display: flex; justify-content: flex-end; padding-top: 12px"
        >
          <span slot="footer">
            <el-button @click="accountVisible = false">取消</el-button>
            <el-button type="primary" @click="handleBatchAccount">
              保存
            </el-button>
          </span>
        </div>
      </div>
    </el-dialog>

    <!-- 年中停账弹窗 -->
    <el-dialog
      title="年中停账"
      :visible.sync="midYearClosureVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px">
        <el-form-item label="停账期间" required>
          <el-select
            v-model="selectedPeriod"
            placeholder="请选择停账期间"
            style="width: 100%"
          >
            <el-option
              v-for="item in periodOptions"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="midYearClosureVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="midYearClosureLoading"
          @click="handleConfirmMidYearClosure"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as config from './config'
import { page as typeSet } from '@/api/financial/setting/book-types/base-data/typeSetup'
import {
  page,
  saveBooks,
  deleteX,
  load,
  enable,
  disable,
  checkInit,
  initType,
  initBooks,
  getBooksInfo,
  initSaveBooks,
  getBaseItemListReport,
  execBaseDataYearEnded,
  checkMidYearClosure,
  execMidYearClosure
} from '@/api/financial/setting/books'
import { userTreeData } from '@/api/system/ognUser'
import { comboJson } from '@/api/gever/common.js'
import { listByDivOrOrgId } from '@/api/financial/setting/book-types'
import { getByBooksTypeId } from '@/api/financial/setting/book-types/base-data/item-param.js'
import { formatDate } from '@/utils/date.js'
import batchAccountCreation from './batchAccountCreation.vue'
import { getToken } from '@/utils/auth'
import { createNamespacedHelpers, mapState } from 'vuex'
const { mapMutations } = createNamespacedHelpers('financial')
export default {
  name: 'Books', // 账套维护
  components: { batchAccountCreation },
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const { settingList } = config
    return {
      ...config,
      baseUrl: process.env.VUE_APP_BASE_API,
      accountVisible: false,
      loading: false,
      saveLoading: false,
      visible: false,
      orgSelectorVisible: false,
      boxLoading: false,
      // 年中停账相关
      midYearClosureVisible: false,
      midYearClosureLoading: false,
      selectedPeriod: null,
      queryParams: {
        page: 1,
        rows: 20,
        booksId: '',
        booksCode: '',
        booksName: '',
        yearEnded: 0,
        createYear: ''
      },
      columns: [
        { type: 'selection', fixed: 'left' },
        { prop: 'booksCode', label: '账套编号', fixed: 'left', minWidth: 100 },
        { prop: 'booksName', label: '账套名称', width: 180, fixed: 'left' },
        { prop: 'orgName', label: '单位', width: 180 },
        { prop: 'booksTypeName', label: '账套类型', width: 180 },
        { prop: 'createYear', label: '建账年份', minWidth: 100 },
        { prop: 'useable', label: '是否启用', minWidth: 100 },
        { prop: 'initialized', label: '是否初始化', width: 120 },
        { prop: 'yearEnded', label: '年结状态', minWidth: 100 },
        { prop: 'midYearClosure', label: '是否年中停账', minWidth: 100 },
        { prop: 'midYearClosurePeriod', label: '停用期间', minWidth: 100 },
        { label: '操作', slotName: 'operation', width: 150, fixed: 'right' }
      ],
      optionsMap: {
        useable: [
          { id: 1, text: '已启用' },
          { id: 0, text: '未启用' }
        ],
        initialized: [
          { id: 1, text: '已初始化' },
          { id: 0, text: '未初始化' }
        ],
        yearEnded: [
          { id: -1, text: '全部' },
          { id: 0, text: '未年结' },
          { id: 1, text: '已年结' }
        ],
        midYearClosure: [
          { id: 1, text: '已停账' },
          { id: 0, text: '未停账' }
        ]
      },
      table: { rows: [], total: 0 },
      rules: {
        booksName: [
          { required: true, message: '账套名称必填', trigger: 'blur' }
        ],
        booksCode: [
          { required: true, message: '编号必填', trigger: 'blur' },
          {
            pattern: /^[0-9a-zA-Z]*$/,
            message: '编号只能填写数字或者英文',
            trigger: 'blur'
          }
        ],
        expenseType: [
          { required: true, message: '账套维护必填', trigger: 'blur' }
        ]
      },
      formDisabled: false,
      form: {
        id: '',
        settingList,
        budget: 0,
        itemType: ''
      },
      choosedData: {}, // 机构地区树选择的数据
      booksTypeOptions: [],
      isEdit: false, //
      bookInfo: {}, // 当前选中的行
      initVisible: false,
      initTypeList: [],
      generateLoading: false,
      accountingItemOptions: [],
      treeNodeAreaName: '',
      treeData: [],
      yearEndedLoading: false,
      orgTypeOptions: [],
    }
  },
  computed: {
    ...mapState('financial', ['booksId']),
    ...mapState({ userId: (state) => state.user.userId }),
    expandedNodes() {
      const ids = []
      if (this.treeData.length > 0) {
        this.treeData.forEach((levelOneNode) => {
          ids.push(levelOneNode.id)
        })
      }
      return ids
    },
    // 禁用 是否需要计提且当期到期的资产必须计提
    disabled514() {
      const index = this.getIndexByCode('513')
      const val = this.form.settingList[index].value === '1'
      if (val) {
        this.off514()
      }
      return val
    },
    selection() {
      return this.$refs['_tableRef'].selection
    },
    selectedIds() {
      return this.selection.map(({ id }) => id).join(',')
    },
    useables() {
      return this.selection.map(({ useable }) => useable).join(',')
    },
    periodOptions() {
      const list = new Array(12)
        .fill(0)
        .map((_, index) => ({ id: index + 1, text: `${index + 1}月` }))
      return list
    },
    yearEndedText() {
      return (yearEnded) =>
        this.optionsMap.yearEnded.find(({ id }) => id === Number(yearEnded))
          ?.text || ''
    }
  },
  watch: {
    initVisible(newValue) {
      if (newValue) {
        this.getInitType()
      }
    }
  },
  created() {
    this.getList()
    this.getUserTypeAll()
    this.loadAllComboJson()
  },
  methods: {
    getToken,
    ...mapMutations(['SET_BOOKS']),
    loadAllComboJson() {
      comboJson({ path: '/system/organization_type/' }).then((res) => {
        this.orgTypeOptions.push(...res.data)
      })
    },
    async getUserTypeAll() {
      const { data: treeData } = await userTreeData({ areaCode: this.areaCode })
      this.treeData = treeData
    },
    handleNodeChange(data) {
      this.$set(this.queryParams, 'areaName', data.text)
      this.$set(this.queryParams, 'areaCode', data.code)
    },
    async loadTree() {
      const params = {}
      if (arguments[0]) {
        params.id = arguments[0]
      }
      return await userTreeData(params.id)
    },
    loadChildNode(id, resolve) {
      this.loadTree({ id: id }).then((res) => {
        resolve(res.data)
      })
    },
    // 关闭 是否需要计提且当期到期的资产必须计提
    off514() {
      const index = this.getIndexByCode('514')
      if (index === -1) {
        this.form.settingList = this.settingList
      }
      this.form.settingList[index].value = '0'
    },
    accountClose() {
      this.accountVisible = false
      this.getList()
    },
    handleGenerateAccount() {
      this.accountVisible = true
    },
    async getTreeOptions(val) {
      console.log('getTreeOptions', val)
      const params = {
        booksTypeId: val
      }
      const res = await getBaseItemListReport(params)
      this.accountingItemOptions = res.data
    },

    // 根据code返回对应的索引值
    getIndexByCode(code) {
      const index = this.form.settingList.findIndex(
        (item) => item.code === code
      )

      return index
      // if (index >= 0) {
      //   return index
      // } else {
      //   return 0
      // }
    },
    async getBookInformation({ id }) {
      try {
        const { data } = await getBooksInfo({ booksId: id })
        this.bookInfo = { ...data }
      } catch (error) {
        console.log(error)
      }
    },
    // 一键根据机构类型生成预设账套
    handleGenerateBooks() {
      this.$confirm('此操作将一键生成机构的预设账套,确定生成吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async (action) => {
          try {
            this.generateLoading = true
            const { message } = await initSaveBooks()
            this.$message.success(message)
            this.handleSearch()
            this.generateLoading = false
          } catch (error) {
            console.log(error)
            this.generateLoading = false
          }
        })
        .catch(() => {
          this.$message.info('取消操作！')
        })
    },
    // 查询
    handleSearch() {
      this.queryParams.page = 1
      this.getList()
    },
    // handleRefresh() {
    //   this.$refs['_searchFormRef'].resetFields()
    //   this.handleSearch()
    // },
    async getList() {
      try {
        this.loading = true
        const { data } = await page(this.queryParams)
        this.table = data
        this.loading = false
      } catch (error) {
        this.table = { rows: [], total: 0 }
        this.loading = false
      }
    },
    async getDetails(id) {
      try {
        const data = await load({ id })
        this.form = { ...data }
      } catch (error) {
        console.log(error)
      }
    },

    handleCurrentChange(data) {
      this.choosedData = { ...data }
    },
    handleChooseOrganization() {
      const { type, id, text, code, parentId } = this.choosedData
      if (type === 'Organization') {
        this.form.orgName = text
        this.form.booksName = text
        this.form.orgId = id
        this.form.orgCode = code
        this.form.divId = parentId
        this.orgSelectorVisible = false
        this.getBookTypes(id)
        this.form.booksTypeId = ''
        this.form.settingList[this.getIndexByCode('010')].value = text
      } else {
        this.$message.warning('请选择机构')
      }
    },
    // 账套类型改变时
    async handleBooksTypeChange(booksTypeId = '') {
      try {
        const booksOption = await typeSet({ booksTypesId: booksTypeId })
        console.log(booksOption, 'booksOption')
        this.form.settingList[this.getIndexByCode('220')].value =
          booksOption.data.find((c) => c.name == '应收款一级科目').value
        this.form.settingList[this.getIndexByCode('221')].value =
          booksOption.data.find((c) => c.name == '应付款一级科目').value
        this.form.settingList[this.getIndexByCode('515')].value =
          booksOption.data.find((c) => c.name == '固定资产一级科目').value
        this.form.settingList[this.getIndexByCode('516')].value =
          booksOption.data.find((c) => c.name == '累计折旧一级科目').value
        this.form.settingList[this.getIndexByCode('517')].value =
          booksOption.data.find((c) => c.name == '固定资产清理一级科目')
            ?.value || ''
        const { data = {}} = await getByBooksTypeId({ booksTypeId })
        const { maxlevel = 1 } = data
        this.form.settingList[this.getIndexByCode('060')].value = maxlevel
        for (let i = 1; i <= 10; i++) {
          this.form.settingList[this.getIndexByCode(`0${60 + i}`)].value =
            data[`levelItem${i}`]
        }
        this.getTreeOptions(this.form.booksTypeId)
      } catch (error) {
        console.log(error)
      }
    },
    async getBookTypes(id, booksTypeId = '') {
      const { data = [] } = await listByDivOrOrgId({
        divOrOrgId: id,
        curBooksTypeId: booksTypeId
      })
      this.booksTypeOptions = data
    },
    handleAdd() {
      this.isEdit = false
      this.formDisabled = false
      console.log(this.settingList, 'this.settingList')
      this.form = {
        accountingUseable: 1,
        assetUseable: 1,
        cashierUseable: 1,
        beginPeriod: '',
        booksCode: '',
        booksName: '',
        booksTypeId: this.booksTypeId,
        createTime: formatDate(),
        isRelevanceBill: 1,
        createYear: '',
        divId: '',
        id: '',
        orgCode: '',
        orgId: '',
        remark: '',
        settingList: this.settingList,
        useable: 1
      }
      this.visible = true
    },
    async handleView(row) {
      this.formDisabled = true
      this.visible = true
      const { id, orgId, booksTypeId, orgName } = row
      await this.getDetails(id)
      this.getBookTypes(orgId, booksTypeId)
      this.form.orgName = orgName
      this.getTreeOptions(this.form.booksTypeId)
    },
    async handleEdit(row) {
      this.formDisabled = false
      this.isEdit = true
      this.visible = true
      const { id, orgId, booksTypeId, orgName } = row
      await this.getDetails(id)
      this.getBookTypes(orgId, booksTypeId)
      this.form.orgName = orgName
      this.getTreeOptions(this.form.booksTypeId)
    },

    getBaseItemListReportOptions(val) {
      getBaseItemListReport({ booksTypeId: val }).then((res) => {
        this.accountingItemOptions = res.data
      })
    },
    handleRemove(row) {
      const id = row?.id || this.selectedIds

      if (row.useable == 1 || this.useables.includes(1)) {
        return this.$message.warning('已启用的账套不允许删除')
      }
      if (id) {
        this.$confirm(
          this.$t(`确定删除${row.id ? '本条' : '已选择'}的账套吗？`),
          this.$t('提示'),
          {
            confirmButtonText: this.$t('确定'),
            cancelButtonText: this.$t('取消'),
            type: 'warning'
          }
        )
          .then(async (action) => {
            if (action === 'confirm') {
              try {
                await deleteX({ booksId: this.booksId, id })
                this.$message.success('操作成功')
                this.getList()
              } catch (error) {
                console.log(error)
              }
            }
          })
          .catch(() => {})
      } else {
        this.$message.warning('至少选择一项数据')
      }
    },
    handleSubmit() {
      this.$refs['_formRef'].validate(async (valid) => {
        if (valid) {
          try {
            this.saveLoading = true
            const {
              assetUseable = 0,
              cashierUseable = 0,
              accountingUseable = 0
            } = this.form
            const sum =
              Number(assetUseable) +
              Number(cashierUseable) +
              Number(accountingUseable)
            if (sum) {
              // 将大于科目级别的值赋值为0
              let summary = 0
              const maxLevel = Number(
                this.form.settingList[this.getIndexByCode(`060`)]
              )
              for (let i = 1; i < 10; i++) {
                if (i > maxLevel) {
                  this.form.settingList[
                    this.getIndexByCode(`0${60 + i}`)
                  ].value = 0
                } else {
                  summary +=
                    Number(
                      this.form.settingList[this.getIndexByCode(`0${60 + i}`)]
                        .value
                    ) || 0
                }
              }
              if (summary > 127) {
                this.$message.warning('科目编号总长度必须小于等于127位')
                return
              }
              const { message } = await saveBooks(this.form)
              this.$message.success(message)
              this.visible = false
              this.$forceUpdate()
              this.getList()
              this.$refs['_selectRef'].getOptions()
            } else {
              this.$message.warning('至少启用一个模块')
            }
            this.saveLoading = false
          } catch (error) {
            console.log(error)
            this.saveLoading = false
          }
        }
      })
    },
    validateLength(maxLength = 9999) {
      const { length = 0 } = this.selection
      if (!length) {
        this.$message.warning('至少选择一项数据')
        return false
      }
      if (length > maxLength) {
        this.$message.warning(`最多选择${maxLength}条数据`)
        return false
      }
      return true
    },
    // 如果停用的是已登录的则清空账套登录
    removeLogin() {
      if (this.selectedIds.includes(this.booksId)) {
        this['SET_BOOKS']()
      }
    },
    // 启用
    async handleStartUsing() {
      if (this.validateLength()) {
        const validate = this.selection.every((item) => !item.useable)
        if (validate) {
          try {
            const { message } = await enable({ id: this.selectedIds })
            this.$message.success(message)
            this.getList()
          } catch (error) {
            console.log(error)
          }
        } else {
          this.$message.warning('已启用的账套不能重复启用')
        }
      }
    },
    // 停用
    async handleStopUsing() {
      if (this.validateLength()) {
        const validate = this.selection.every((item) => item.useable)
        if (validate) {
          try {
            const { message } = await disable({ id: this.selectedIds })
            this.$message.success(message)
            this.removeLogin()
            this.getList()
          } catch (error) {
            console.log(error)
          }
        } else {
          this.$message.warning('已停用的账套不能停用')
        }
      }
    },
    handleBatchAccount() {
      this.$refs.batchref.handleSubmit()
    },
    // 初始化
    async handleInitialize() {
      if (this.validateLength(1)) {
        const validate = this.selection.every((item) => !item.initialized)
        if (validate) {
          try {
            const { returnCode, message } = await checkInit({
              id: this.selectedIds
            })
            if (returnCode === '0') {
              this.initVisible = true
            }
            if (returnCode === '-1') {
              this.$message.error(message)
            }
          } catch (error) {
            console.log(error)
          }
        } else {
          this.$message.warning('当前账套的账套余额已初始化，不能重置基础数据')
        }
      }
    },
    async getInitType() {
      try {
        const { data = [] } = await initType()
        this.initTypeList = data || []
      } catch (error) {
        console.log(error)
      }
    },
    async handleInitBooks() {
      try {
        const modelIds = this.initTypeList
          .filter(({ checked }) => checked)
          .map(({ id }) => id)
        const { message } = await initBooks({
          booksId: this.selectedIds,
          modelIds
        })
        this.$message.success(message)
        this.handleSearch()
        this.initVisible = false
      } catch (error) {
        console.log(error)
      }
    },
    handleExportTemp() {
      if (this.table.rows.length == 0) {
        this.$message.warning(this.$t('没有需要导出的数据！'))
        return false
      }
      let str = ''
      const obj = { ...this.queryParams }
      delete obj.rows
      delete obj.page
      for (var i = 0; i < Object.keys(obj).length; i++) {
        if (str) {
          str += '&'
        }
        str += Object.keys(obj)[i] + '=' + Object.values(obj)[i]
      }
      const url =
        this.baseUrl +
        `/financial/books/exportTemp?` +
        str +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.location.href = url
    },
    handleYearEndedBaseData() {
      if (this.validateLength(1)) {
        const { useable, initialized, yearEnded } = this.selection[0]
        if (useable != 1) {
          this.$message.warning(this.$t('当前账套未启用！'))
          return false
        }
        if (initialized != 1) {
          this.$message.warning(this.$t('当前账套未完成余额初始化！'))
          return false
        }
        if (yearEnded != 0) {
          this.$message.warning(this.$t('当前账套已年结！'))
          return false
        }
        this.$confirm('此操作会将当前账套以及对应的基础数据年结到下一年，确定结转吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              this.yearEndedLoading = true
              const res = await execBaseDataYearEnded({ booksId: this.selectedIds })
              if (res.returnCode == '0') {
                this.yearEndedLoading = false
                this.$message.success(res.message)
                this.handleSearch()
              } else {
                this.yearEndedLoading = false
              }
            } catch (error) {
              console.log(error)
              this.yearEndedLoading = false
            }
          })
          .catch(() => {
          })
      }
    },
    // 年中停账
    handleMidYearClosure() {
      if (this.validateLength(1)) {
        const { useable, initialized, yearEnded } = this.selection[0]
        if (useable !== 1) {
          this.$message.warning(this.$t('当前账套未启用！'))
          return false
        }
        if (initialized !== 1) {
          this.$message.warning(this.$t('当前账套未完成余额初始化！'))
          return false
        }
        if (yearEnded !== 0) {
          this.$message.warning(this.$t('当前账套已年结！'))
          return false
        }
        this.selectedPeriod = null
        this.midYearClosureVisible = true
      }
    },
    // 确认年中停账
    async handleConfirmMidYearClosure() {
      if (!this.selectedPeriod) {
        this.$message.warning(this.$t('请选择停账期间'))
        return
      }

      try {
        this.midYearClosureLoading = true
        const params = {
          booksId: this.selectedIds,
          closurePeriod: Number(this.selectedPeriod)
        }

        // 第一步：调用检验接口
        const checkResult = await checkMidYearClosure(params)
        if (checkResult.returnCode !== '0') {
          this.$message.error(checkResult.message)
          this.midYearClosureLoading = false
          return
        }

        if (checkResult.message && checkResult.message.trim() !== '') {
          // 如果检验返回的字符串不为空，提示用户
          this.$confirm(checkResult.message, this.$t('提示'), {
            confirmButtonText: this.$t('确定'),
            cancelButtonText: this.$t('取消'),
            type: 'warning'
          }).then(async () => {
            // 用户确认后调用第二个停账确认接口
            await this.executeMidYearClosure(params)
          }).catch(() => {
            this.midYearClosureLoading = false
          })
        } else {
          // 检验通过，直接确认停账
          this.$confirm(this.$t('确定要进行年中停账吗？'), this.$t('提示'), {
            confirmButtonText: this.$t('确定'),
            cancelButtonText: this.$t('取消'),
            type: 'warning'
          }).then(async () => {
            await this.executeMidYearClosure(params)
          }).catch(() => {
            this.midYearClosureLoading = false
          })
        }
      } catch (error) {
        console.log(error)
        this.midYearClosureLoading = false
      }
    },
    // 执行年中停账
    async executeMidYearClosure(params) {
      try {
        const { message } = await execMidYearClosure(params)
        this.$message.success(message)
        this.midYearClosureVisible = false
        this.getList()
      } catch (error) {
        console.log(error)
        this.$message.error(this.$t('年中停账失败'))
      } finally {
        this.midYearClosureLoading = false
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
