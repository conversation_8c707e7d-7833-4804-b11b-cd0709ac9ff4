<!-- 发票开出 -->
<template>
  <div>
    <fold-box
      v-if="areaLogin.level === 5"
      :left-title="$t('发票类型（显示简称）')"
      :right-title="$t('发票开出')"
    >
      <template #left>
        <div v-loading="loading" class="left-box">
          <ul>
            <li
              v-for="(item, index) in leftList"
              :key="index"
              :class="nowSelectLeft === index ? 'active' : ''"
              @click="handleLi(index)"
            >
              {{ $t(item.abbr) }}
            </li>
          </ul>
        </div>
      </template>
      <template #right>
        <div v-loading="loading" class="right-box">
          <el-form
            ref="_searchFormRef"
            :inline="true"
            :model="form"
            :rules="rules"
          >
            <el-form-item :label="$t('发票号码：')" prop="invoiceName">
              <el-input
                v-model="form.invoiceName"
                type="text"
                :clearable="true"
                class="w120"
              />
            </el-form-item>
            <el-form-item v-if="invoiceTypeNow.media !== 3" :label="$t('开始序号：')" prop="beginSeq">
              <el-input
                v-model="form.beginSeq"
                :disabled="false"
                :clearable="true"
                class="w100"
                oninput="value=value.replace(/[^\d]/g,'')"
              />
            </el-form-item>
            <el-form-item v-if="invoiceTypeNow.media !== 3" :label="$t('结束序号：')" prop="endSeq">
              <el-input
                v-model="form.endSeq"
                :disabled="false"
                :clearable="true"
                class="w100"
                oninput="value=value.replace(/[^\d]/g,'')"
              />
            </el-form-item>
            <el-form-item :label="$t('付款方：')" prop="payerName">
              <el-input
                v-model="form.payerName"
                type="text"
                :clearable="true"
                class="w100"
              />
            </el-form-item>
            <el-form-item prop="startDate" :label="$t('开票日期：')">
              <el-date-picker
                v-model.trim="form.startDate"
                style="width: 125px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            -
            <el-form-item prop="endDate" style="margin-left: 10px">
              <el-date-picker
                v-model.trim="form.endDate"
                style="width: 125px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item>
              <el-popover
                v-model="advanceVisible"
                trigger="manual"
                popper-class="advance-search"
                width="380"
              >
                <h3>{{ $t('高级搜索') }}</h3>
                <el-form
                  ref="_advanceFormRef"
                  inline
                  label-width="100px"
                  :model="form"
                  label-position="right"
                >
                  <div class="form-sg">
                    <el-form-item :label="$t('收入类型')" prop="incomeType">
                      <gever-select
                        clearable
                        v-model="form.incomeType"
                        :options="incomeTypeOptions"
                      />
                    </el-form-item>
                  </div>
                  <div class="form-sg" v-if="invoiceTypeNow.media === 1">
                    <el-form-item :label="$t('打印状态')" prop="printState">
                      <gever-select
                        clearable
                        v-model="form.printState"
                        :options="printStateOptions"
                      />
                    </el-form-item>
                  </div>
                  <div class="form-sg">
                    <el-form-item :label="$t('收款状态')" prop="oldPassword">
                      <gever-select
                        v-model="form.partCollection"
                        :options="isPayList"
                        :value-prop="'value'"
                      />
                    </el-form-item>
                  </div>
                  <div class="form-sg">
                    <el-form-item :label="$t('发票状态')" prop="invoiceState">
                      <gever-select
                        clearable
                        v-model="form.invoiceState"
                        :options="invoiceStateOptions"
                      />
                    </el-form-item>
                  </div>
                  <div class="form-sg">
                    <el-form-item :label="$t('红冲状态')" prop="offsetType">
                      <gever-select
                        clearable
                        v-model="form.offsetType"
                        :options="offsetTypeOptions"
                      />
                    </el-form-item>
                  </div>
                  <div class="form-sg">
                    <el-form-item :label="$t('开票人')" prop="oldPassword">
                      <GeverInput v-model.trim="form.drawer" clearable />
                    </el-form-item>
                  </div>
                  <div class="form-sg">
                    <el-form-item :label="$t('摘要')" prop="oldPassword">
                      <GeverInput v-model.trim="form.advSummary" clearable />
                    </el-form-item>
                  </div>
                </el-form>
                <div style="float: right">
                  <el-button
                    type="primary"
                    round
                    :disabled="areaLogin.type !== 'Organization'"
                    @click="handleRresetForm"
                  >
                    {{ $t('重置') }}
                  </el-button>
                  <el-button
                    type="primary"
                    round
                    :disabled="areaLogin.type !== 'Organization'"
                    @click="handleAdvanceSearch"
                  >
                    {{ $t('搜索') }}
                  </el-button>
                  <el-link
                    type="primary"
                    :underline="false"
                    icon="el-icon-arrow-up"
                    style="margin-left: 10px"
                    @click="handlePackUp"
                  >
                    收起
                  </el-link>
                </div>
                <span
                  slot="reference"
                  style="font-size: 14px; color: #409eff; cursor: pointer"
                  @click="handleShowHide"
                >
                  高级搜索
                </span>
              </el-popover>
            </el-form-item>
            <div class="btn-box">
              <el-button type="primary" round @click="handleSearch">
                {{ $t('搜索') }}
              </el-button>
            </div>
          </el-form>
          <div class="function-btn">
            <div>
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.save'"
                type="primary"
                round
                :disabled="loading"
                @click="handleAdd"
              >
                {{ $t('开票') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media === 3">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.sendDigital'"
                type="default"
                round
                @click="handleSendDigital"
              >
                {{ $t('发送到税务') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media === 3">
              <el-button
                v-hasPermi="
                  'financial.invoice.invoiceIssued.refreshInvoiceState'
                "
                type="default"
                round
                @click="handleRefreshState"
              >
                {{ $t('更新开票状态') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media !== 3">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.sendTaxControl'"
                type="default"
                round
                @click="handleSend"
              >
                {{ $t('发送到税务') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media === 1">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.updatePrintState'"
                type="default"
                round
                @click="hanldeHit"
              >
                {{ $t('套打') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media === 1">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.reprint'"
                type="default"
                round
                @click="handlePrint"
              >
                {{ $t('补打') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media !== 3">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.cancellation'"
                type="default"
                round
                @click="handleVoided"
              >
                {{ $t('作废') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media !== 3">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.invoiceLose'"
                type="default"
                round
                @click="handleLost"
              >
                {{ $t('遗失') }}
              </el-button>
            </div>
            <div>
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.invoiceOffset'"
                type="default"
                round
                :disabled="loading"
                @click="handleOffset"
              >
                {{ $t('红冲') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media !== 1">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.getInvoiceURL'"
                type="default"
                round
                @click="handleDownloads"
              >
                {{ $t('下载发票') }}
              </el-button>
            </div>
            <div>
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.supply'"
                type="default"
                round
                :disabled="loading"
                @click="handleSupply"
              >
                {{ $t('补开') }}
              </el-button>
            </div>
            <div
              v-show="invoiceTypeNow.media !== 3 && invoiceTypeNow.kind !== 2"
            >
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.editNotice'"
                type="default"
                round
                @click="handleEditNotice"
              >
                {{ $t('修改通知单号') }}
              </el-button>
            </div>
            <div v-show="invoiceTypeNow.media === 1">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.getInvoiceById'"
                type="default"
                round
                @click="handlePreview()"
              >
                {{ $t('发票预览') }}
              </el-button>
            </div>
            <div
              v-if="
                hasPermission('financial.invoice.invoiceIssued.relateCashier')
              "
            >
              <el-button
                type="default"
                round
                :disabled="areaLogin.type !== 'Organization'"
                @click="handleRelateCashier"
              >
                {{ $t('关联出纳') }}
              </el-button>
            </div>
            <div
              v-if="
                hasPermission(
                  'financial.invoice.invoiceIssued.cancelRelateCashier'
                )
              "
            >
              <el-button
                type="default"
                round
                :disabled="areaLogin.type !== 'Organization'"
                @click="cancelRelateCashier"
              >
                {{ $t('取消关联出纳') }}
              </el-button>
            </div>
            <div
              v-if="
                hasPermission('financial.invoice.invoiceIssued.relateAccount')
              "
            >
              <el-button
                type="default"
                round
                :disabled="areaLogin.type !== 'Organization'"
                @click="handleRelateAccounting"
              >
                {{ $t('关联会计') }}
              </el-button>
            </div>
            <div
              v-if="
                hasPermission(
                  'financial.invoice.invoiceIssued.cancelRelateAccount'
                )
              "
            >
              <el-button
                type="default"
                round
                :disabled="areaLogin.type !== 'Organization'"
                @click="cancelRelateAccounting"
              >
                {{ $t('取消关联会计') }}
              </el-button>
            </div>
            <div>
              <el-button
                v-if="
                  $store.state.user.name.includes('admin') ||
                    $store.state.user.name.includes('管理员')
                "
                icon=""
                plain
                type="primary"
                round
                @click="updateBillInvoicingStage()"
              >
                {{ $t('转先收后开') }}
              </el-button>
            </div>
            <div>
              <el-button
                v-if="
                  $store.state.user.name.includes('admin') ||
                    $store.state.user.name.includes('管理员')
                "
                icon=""
                plain
                type="primary"
                round
                @click="updateBillInvoicingStage2()"
              >
                {{ $t('转先开后收') }}
              </el-button>
            </div>
          </div>
          <gever-table
            ref="_geverTableRef"
            :columns="tableColumns"
            :data="table.rows"
            highlight-current-row
            pagination
            :page-sizes="[20, 50, 100, 200, 500, 800, 1000]"
            show-summary
            :summary-method="handleSummary"
            :total="table.total"
            :pagi="form"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
          >
            <template #invoiceNo="{ row }">
              <template v-if="row.offsetType === 2">
                <span class="red">{{ row.invoiceNo }}</span>
              </template>
              <template v-else>
                {{ row.invoiceNo }}
              </template>
            </template>
            <template #contractCode="{ row }">
              <div
                v-for="(item, i) in row.contractInfoVOList"
                :key="item.contractId"
              >
                <el-button
                  v-hasPermi="'financial.invoice.invoiceIssued.getInvoiceById'"
                  type="text"
                  class="copyable-text"
                  @click="contractView(item.contractId)"
                >
                  {{ item.contractCode }}
                </el-button>
                <br v-if="i && i <= row.contractInfoVOList.length" />
              </div>
            </template>
            <template #uncollected="{ row }">
              {{
                row.offsetType === 1
                  ? countUncollected(row)
                  : comma_r(row.totalAmount * 1 - row.receivedTotalAmount * 1)
              }}
            </template>
            <template #receivedTotalAmount="{ row }">
              {{ comma_r(row.receivedTotalAmount) }}
            </template>
            <template #saleAmount="{ row }">
              {{ comma_r(row.saleAmount) }}
            </template>
            <template #taxAmount="{ row }">
              {{ comma_r(row.taxAmount) }}
            </template>
            <template #taxableAmount="{ row }">
              {{ comma_r(row.taxableAmount) }}
            </template>
            <template #totalAmount="{ row }">
              {{ comma_r(row.totalAmount) }}
            </template>
            <template #invoiceState="{ row }">
              <span :class="row.invoiceState === -1 && 'red'">
                {{ invoiceStateText(row.invoiceState) }}
              </span>
            </template>
            <template #offsetType="{ row }">
              <template v-if="row.offsetType === 0">
                {{ offsetText(row.offsetType) }}
              </template>
              <template v-else-if="row.offsetType === 1">
                {{ offsetText(row.offsetType) + '(' }}
                <span class="red">{{ row.offsetSourceNo }}</span>
                {{ ')' }}
              </template>
              <template v-else>
                <span class="red">{{ offsetText(row.offsetType) }}</span>
                {{ '(' + row.offsetInvoiceNo + ')' }}
              </template>
            </template>
            <template #isRecorded="{ row }">
              {{
                row.isRecorded === 2
                  ? $t('出纳账')
                  : row.isRecorded === 1
                    ? $t('会计账')
                    : $t('未记账')
              }}
            </template>
            <template #operation="{ row }">
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.getInvoiceById'"
                type="text"
                @click="handleView(row)"
              >
                {{ $t('查看') }}
              </el-button>
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.save'"
                :disabled="
                  row.offsetType !== 0 ||
                    (invoiceTypeNow.media === 3 && row.invoiceState > 0) ||
                    (invoiceTypeNow.media !== 3 && row.dockState === 1)
                "
                type="text"
                @click="handleEdit(row)"
              >
                {{ $t('编辑') }}
              </el-button>
              <el-button
                v-hasPermi="'financial.invoice.invoiceIssued.deleteInvoice'"
                :disabled="
                  (invoiceTypeNow.media === 3 && row.invoiceState > 0) ||
                    (invoiceTypeNow.media !== 3 && row.dockState !== 0)
                "
                type="text"
                @click="handleDelete(row)"
              >
                {{ $t('删除') }}
              </el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <!-- 登录电子税务局 -->
    <login-swj-dialog
      v-if="loginSwjDialogVisible"
      :dialog-visible.sync="loginSwjDialogVisible"
      :registry="registry"
      @successLoginSwj="handleSuccessLoginSwj"
    ></login-swj-dialog>
    <!-- 扫码认证 -->
    <qr-code-dialog
      v-if="scanDialogVisible"
      :dialog-visible.sync="scanDialogVisible"
      :registry="registry"
      @successScanCode="handleSuccessScanCode"
    ></qr-code-dialog>
    <el-dialog
      v-if="scanDialogVisible"
      title="认证已过期，请重新认证"
      width="380px"
      top="15vh"
      class="scanDialogTitle"
      :append-to-body="false"
      :visible.sync="scanDialogVisible"
      @close="handleCloseQrCode"
    >
      <div v-loading="qrCodeLoading" style="text-align: center; height: 360px">
        <div ref="_scanTop" class="scanTop">
          <div
            v-for="(item, index) in ['税务APP', '个税APP']"
            :key="index"
            :class="['scanTitle', { scanSelected: smlx === String(index) }]"
            @click="scanTitleClick(String(index))"
          >
            <span>{{ item }}</span>
          </div>
        </div>
        <div id="swjQrCode" ref="_swjQrCode">
          <img
            ref="_refreshImg"
            src="../../../../assets/images/refresh_logo.png"
          />
        </div>
        <div
          style="width: 100%; height: 22px; overflow: hidden; margin: 0 auto"
        >
          <span ref="_secondText" style="line-height: 22px"></span>
        </div>
        <el-button type="primary" round @click="handleRefreshQrCode">
          {{ $t('刷新二维码') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 发票预览 -->
    <el-dialog
      v-if="previewVisible"
      title="发票预览"
      :visible.sync="previewVisible"
      :append-to-body="false"
      top="5vh"
      width="1080px"
    >
      <section v-loading="previewLoading" class="preview-content font-y">
        <header class="font-y df">
          <article class="code font-y df">
            <div id="qrcode_" ref="qrCodeUrl" />
            <aside>
              <p>{{ printData.invoiceCode }}</p>
              <p class="font-b">
                <span>机器编号:</span>
                <span>{{ printData.machineCode }}</span>
              </p>
            </aside>
          </article>
          <article class="title font-y tc">
            <!-- invoiceKind 1 是增值税专用 2是普通发票 -->
            <h3 class="title">
              广东增值税{{ printData.invoiceKind === 1 ? '专用' : '普通' }}发票
            </h3>
            <p class="double-line pb10">_____________</p>
            <p class="title">发票联</p>
          </article>
          <article class="number font-y tr">
            <p>
              <span class="icon font-d">№</span>
              <span class="big font-b">{{ printData.invoiceNo }}</span>
              <span class="small font-b">{{ printData.invoiceCode }}</span>
            </p>
            <p class="font-b">{{ printData.invoiceNo }}</p>
            <p>
              开票日期:
              <span class="date font-b">{{ printData.drawDate }}</span>
            </p>
          </article>
        </header>
        <section class="table-content font-y">
          <article class="purchase df">
            <p class="buyer">购 买 方</p>
            <ul class="info">
              <li>
                名称：
                <span class="font-b">{{ printData.payerName }}</span>
              </li>
              <li>
                纳税人识别号：
                <span class="font-b">{{ printData.payerTaxpayerNo }}</span>
              </li>
              <li>
                地址、电话：
                <span class="font-b">{{ printData.payerContactInfor }}</span>
              </li>
              <li>
                开户行及账号：
                <span class="font-b">{{ printData.payerAccountInfor }}</span>
              </li>
            </ul>
            <p class="password">密 码 区</p>
            <p class="pw font-b">{{ printData.password }}</p>
          </article>
          <ul class="goods df ls3">
            <li class="name tc">货物或应税劳务、服务名称</li>
            <li class="model tc">规格型号</li>
            <li class="unit tc">单位</li>
            <li class="quantity tc">数量</li>
            <li class="price tc">单价</li>
            <li class="money tc">金额</li>
            <li class="tax-rate tc">税率</li>
            <li class="tax-amount tc">税额</li>
          </ul>
          <ul
            v-for="item in printData.invoiceDetailItemList || []"
            :key="item.id"
            class="df font-b goods"
            :style="{
              'min-height':
                printData.invoiceDetailItemList &&
                printData.invoiceDetailItemList.length > 1
                  ? '100%'
                  : '100px'
            }"
          >
            <li class="name tl">
              *{{ item.taxKindName }}*{{ item.chargeItem }}
            </li>
            <li class="model tl"></li>
            <li class="unit tc">{{ item.unit }}</li>
            <li class="quantity tr">{{ item.qty }}</li>
            <li class="price tr">{{ item.taxablePrice | formatMoney9 }}</li>
            <li class="money tr">{{ item.taxableAmount | formatMoney }}</li>
            <li class="tax-rate tr">
              {{ item.taxRate === -1 ? $t('免税') : $t(item.taxRate + '%') }}
            </li>
            <li class="tax-amount tr">
              {{
                item.taxRate === -1 ? $t('***') : item.taxAmount | formatMoney
              }}
            </li>
          </ul>
          <ul class="goods df">
            <li class="name tc count">合计</li>
            <li class="model tc"></li>
            <li class="unit tc"></li>
            <li class="quantity tc"></li>
            <li class="price tc"></li>
            <li class="money tr font-b">
              ￥{{ printData.taxableAmount | formatMoney }}
            </li>
            <li class="tax-rate tr"></li>
            <li class="tax-amount tr font-b">￥{{ conversionTaxAmount() }}</li>
          </ul>
          <article class="money-count df">
            <p class="count tc ls3">价税合计（大写）</p>
            <div class="df uppercase">
              <p class="font-b">
                ⓧ{{ conversionAmount(printData.totalAmount) }}
              </p>
              <p class="df">
                <span class="pr20 ls3">（小写）</span>
                <span class="font-b">
                  ¥ {{ printData.totalAmount | formatMoney }}
                </span>
              </p>
            </div>
          </article>
          <article class="sale df">
            <p class="buyer">销 售 方</p>
            <ul class="info">
              <li>
                名称：
                <span class="font-b">{{ printData.sellerName }}</span>
              </li>
              <li>
                纳税人识别号：
                <span class="font-b">{{ printData.sellerTaxpayerNo }}</span>
              </li>
              <li>
                地址、电话：
                <span class="font-b">{{ printData.sellerContactInfor }}</span>
              </li>
              <li>
                开户行及账号：
                <span class="font-b">{{ printData.sellerAccountInfor }}</span>
              </li>
            </ul>
            <p class="remark">备注</p>
            <p class="rm font-b" v-html="printData.remark"></p>
          </article>
        </section>
        <ul class="footer font-y df">
          <li>
            收款人:
            <span class="font-b">{{ printData.payee }}</span>
          </li>
          <li>
            复核人:
            <span class="font-b">{{ printData.reviewer }}</span>
          </li>
          <li>
            开 票:
            <span class="font-b">{{ printData.drawer }}</span>
          </li>
          <li>销售方: (章)</li>
        </ul>
      </section>
    </el-dialog>
    <!-- 合同查看 -->
    <public-drawer
      :visible.sync="contractVisible"
      :title="$t('查看收款合同')"
      :size="85"
    >
      <collection-contract
        v-if="contractVisible"
        :id="contractId"
        ref="formData"
        load-type="view"
        :change-record-data="[]"
        :contract-variation="true"
        :contract-login="{}"
      />
    </public-drawer>
    <details-dialog
      v-if="detailsVisible"
      :dialog-visible.sync="detailsVisible"
      :invoice-type-select="invoiceTypeSelect"
      :notice-code="noticeCode"
      :status-type="statusType"
      :row-data="nowRowDatac"
      @updatelist="getList"
    ></details-dialog>
    <!-- 数电开票 -->
    <digital-details
      v-if="digitalDetailsVisible"
      :dialog-visible.sync="digitalDetailsVisible"
      :invoice-type-select="invoiceTypeSelect"
      :notice-code="noticeCode"
      :status-type="statusType"
      :row-data="nowRowDatac"
      :org-id="areaLogin.id"
      @updateList="getList"
    ></digital-details>
    <lost
      v-if="lostVisible"
      :dialog-visible.sync="lostVisible"
      :invoice-type-select="invoiceTypeSelect"
      @updatelist="getList"
    ></lost>
    <voided
      v-if="voidedVisible"
      :dialog-visible.sync="voidedVisible"
      :row-data="nowRowDatac"
      @updatelist="getList"
    ></voided>
    <!-- 关联出纳弹窗 -->
    <relate-cashier
      v-if="relateCashierVisible"
      :dialog-visible.sync="relateCashierVisible"
      :total-amount="selectedTotalAmount"
      :selected-invoice-ids="selectedInvoiceIds"
      :page-flag="relatePageFlag"
      @updateTable="getList"
    ></relate-cashier>
    <!-- 关联会计弹窗 -->
    <relate-accounting
      v-if="relateAccountingVisible"
      :dialog-visible.sync="relateAccountingVisible"
      :total-amount="selectedTotalAmount"
      :selected-invoice-ids="selectedInvoiceIds"
      :page-flag="relatePageFlag"
      @updateTable="getList"
    ></relate-accounting>
  </div>
</template>

<script>
import {
  deleteInvoice,
  getbillPrintStyle,
  getIncomeType,
  getInvoiceById,
  getInvoiceURL,
  getPrintData,
  invoiceDetailPage,
  loadTypeList,
  refreshInvoiceState,
  reprint,
  sendDigital,
  sendTaxControl,
  updateInvoicingStage,
  updateInvoicingStage2,
  updateNoticeCode,
  updatePrintState
} from '@/api/financial/invoice/invoiceIssued.js'
import { convertCurrency, openPrintDesign } from './print/printInvoice.js'
import detailsDialog from './components/details.vue'
import digitalDetails from './components/digitalDetails.vue'
import lost from './components/lost.vue'
import voided from './components/voided.vue'
import { createNamespacedHelpers } from 'vuex'
import { getDictionary } from '@/api/gever/common.js'
import { comma } from '@/utils/gever'
import QRCode from 'qrcodejs2'
import moment from 'moment'
import { formatMoney, formatMoney9 } from '@/filter'
import relateCashier from './components/RelateCashier.vue'
import relateAccounting from './components/RelateAccounting.vue'
import LoginSwjDialog from './components/loginSwjDialog.vue'
import QrCodeDialog from './components/qrCodeDialog.vue'
import {
  getRegistryByOrg,
  getSwjLoginStatus
} from '@/api/financial/invoice/invoiceRegistry.js'

const { mapState } = createNamespacedHelpers('area')

export default {
  name: 'InvoiceIssued',
  components: {
    digitalDetails,
    detailsDialog,
    lost,
    voided,
    relateCashier,
    relateAccounting,
    LoginSwjDialog,
    QrCodeDialog
  },
  data() {
    return {
      printData: {},
      contractId: '',
      previewLoading: false,
      previewVisible: false,
      contractVisible: false,
      advanceVisible: false,
      statusType: '',
      loading: false,
      previewSrc: require('./print/zyfp.jpg'),
      form: {
        incomeType: '',
        invoiceName: '',
        payerName: '',
        printState: '',
        invoiceTypeId: '',
        startDate: '',
        invoiceState: '',
        offsetType: '',
        endDate: '',
        isInvoiceQuery: 1, // 是否在发票开出查询
        page: 1,
        rows: 10
      },
      // optionsCascader: [],
      incomeTypeOptions: [
        { id: 1, text: '合同收入' },
        { id: 2, text: '非合同收入' }
      ],
      rules: {},
      printColumn: {
        prop: 'printState',
        label: '打印状态',
        minWidth: 80,
        align: 'center',
        convert: {
          options: [
            { id: 0, text: '未打印' },
            { id: 1, text: '已打印' }
          ]
        }
      },
      tableColumns: [
        { type: 'selection', fixed: 'left' },
        {
          prop: 'invoiceNo',
          label: '发票号码',
          minWidth: 168,
          align: 'center'
        },
        {
          prop: 'dockState',
          label: '税务状态',
          minWidth: 80,
          align: 'center',
          convert: {
            options: [
              { id: 0, text: '未对接' },
              { id: 1, text: '已对接' }
            ]
          }
        },
        { prop: 'drawDate', label: '开票日期', minWidth: 120, align: 'center' },
        { prop: 'drawer', label: '开票人', minWidth: 80, align: 'center' },
        {
          prop: 'saleAmount',
          label: '商品/服务金额',
          minWidth: 110,
          align: 'right'
        },
        { prop: 'taxAmount', label: '税额', minWidth: 100, align: 'right' },
        {
          prop: 'taxableAmount',
          label: '应税金额',
          minWidth: 100,
          align: 'right'
        },
        {
          prop: 'totalAmount',
          label: '价税合计',
          minWidth: 100,
          align: 'right'
        },
        {
          prop: 'receivedTotalAmount',
          label: '实收金额',
          minWidth: 100,
          align: 'right'
        },
        {
          prop: 'uncollected',
          label: '未收金额',
          minWidth: 100,
          align: 'right'
        },
        {
          prop: 'invoiceState',
          label: '发票状态',
          minWidth: 80,
          align: 'center'
        },
        {
          prop: 'offsetType',
          label: '红冲状态',
          minWidth: 120,
          align: 'center'
        },
        {
          prop: 'incomeType',
          label: '收入类型',
          minWidth: 100,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '合同收入' },
              { id: 2, text: '非合同收入' }
            ]
          }
        },
        { prop: 'contractCode', width: 150, label: '合同编号' },
        {
          prop: 'invoicingStage',
          label: '开票方式',
          minWidth: 100,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '先收后开' },
              { id: 2, text: '先开后收' },
              { id: 3, text: '补开' }
            ]
          }
        },
        { prop: 'isRecorded', width: 100, label: '记账状态', align: 'center' },
        {
          prop: 'verifyState',
          label: '核销状态',
          minWidth: 100,
          align: 'center',
          convert: { path: '/financial/invoice/verifyState/', numberKey: true }
        },
        { prop: 'payerName', label: '付款方', minWidth: 120, align: 'left' },
        { prop: 'summary', label: '摘要', minWidth: 120, align: 'left' },
        { prop: 'remark', label: '备注', minWidth: 120, align: 'left' },
        {
          prop: 'disposeReason',
          label: '处置原因',
          minWidth: 120,
          align: 'left'
        },
        {
          prop: 'statusResult',
          label: '发票状态备注',
          minWidth: 135,
          align: 'left'
        },
        { slotName: 'operation', label: '操作', width: 120, fixed: 'right' }
      ],
      table: {
        rows: [],
        total: 0
      },
      nowRowData: [],
      nowRowDatac: {},
      printStateOptions: [
        { id: 0, text: '未打印' },
        { id: 1, text: '已打印' }
      ],
      isPayList: [
        { text: '全部', value: '' },
        { text: '未收款', value: '0' },
        { text: '部分收款', value: '1' },
        { text: '完成收款', value: '2' }
      ],
      detailsVisible: false,
      digitalDetailsVisible: false,
      lostVisible: false,
      voidedVisible: false,
      leftList: [],
      nowSelectLeft: 0,
      invoiceTypeNow: {},
      invoiceTypeSelect: {},
      noticeCode: '',
      invoiceStateOptions: [
        { id: -1, text: '开票失败' },
        { id: 0, text: '开票中' },
        { id: 1, text: '已开出' },
        { id: 2, text: '已作废' },
        { id: 3, text: '遗失' }
      ],
      offsetTypeOptions: [
        { id: 0, text: '正常' },
        { id: 1, text: '红冲' },
        { id: 2, text: '被红冲' }
      ],
      relateCashierVisible: false,
      relateAccountingVisible: false,
      selectedTotalAmount: 0,
      selectedInvoiceIds: '',
      relatePageFlag: 'edit',
      loginSwjDialogVisible: false, // 登录电子税务局账号
      scanDialogVisible: false, // 是否需要扫描认证
      registry: {} // 有效期内的注册订购信息
    }
  },
  computed: {
    ...mapState(['areaLogin']), // 地区机构数据
    // table选择数据
    selectRows() {
      return this.$refs['_geverTableRef'].selection
    },
    // 转浮点数
    comma_r() {
      return (val, keep_dec = 2) => {
        if (val) {
          if (keep_dec) {
            val = Number(val).toFixed(keep_dec)
          }
          val = String(val)
          if (val.indexOf('.') === -1) {
            return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
          }
          return (
            val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
            '.' +
            val.split('.')[1]
          )
        } else {
          return '0.00'
        }
      }
    }
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler() {
        this.getOptions()
      }
    }
  },
  mounted() {
    // this.getOptions()
    if (this.areaLogin.level !== 5) {
      this.$message.warning('请选择机构')
    }
  },
  methods: {
    // 判断是否有权限
    hasPermission(value) {
      const operationCodes = JSON.parse(
        localStorage.getItem('userInfo')
      ).operationCodes
      return operationCodes.indexOf(value) >= 0
    },
    formatMoney9,
    // 如果税率为免税的，税额应该显示***
    conversionTaxAmount() {
      let flag = false
      this.printData.invoiceDetailItemList?.forEach((item) => {
        if (item.taxRate !== -1) {
          flag = true
        }
      })
      if (flag) {
        return formatMoney(this.printData.taxAmount)
      } else {
        return '***'
      }
    },
    conversionAmount(val) {
      if (!val) return ''
      const money = convertCurrency(val)
      return money
    },
    handlePreview() {
      if (this.nowRowData.length !== 1) {
        return this.$message.warning('请选择一条数据')
      }
      if (this.nowRowData[0].printState !== 0) {
        return this.$message.warning('只能预览未打印的发票')
      }
      this.previewLoading = true
      this.previewVisible = true
      this.loadPrintData(this.nowRowData[0].id)
        .then((res) => {
          const qrCodeDom = this.$refs['qrCodeUrl']
          const qrcode = new QRCode(qrCodeDom, {
            width: 100,
            height: 100,
            colorLight: 'transparent',
            correctLevel: 1,
            colorDark: '#0062bd'
          })
          qrcode.makeCode('http://192.168.5.153/financial/cashier/voucher')
          this.printData = {
            ...res,
            drawDate: moment(res.drawDate).format('YYYY年MM月DD日')
          }
        })
        .finally(() => {
          this.previewLoading = false
        })
    },
    contractView(id) {
      this.contractId = id
      this.contractVisible = true
    },
    invoiceStateText(id) {
      const obj = {
        '-1': '开票失败',
        0: '开票中',
        1: '已开出',
        2: '已作废',
        3: '遗失'
      }
      return obj[id + '']
    },
    offsetText(id) {
      const obj = {
        0: '正常',
        1: '红冲',
        2: '被红冲'
      }
      return obj[id]
    },
    countUncollected(row) {
      const totalAmount = row.totalAmount * 1
      let receivedTotalAmount = 0
      if (row.offsetType === 1) {
        // 红冲票的已收款也应该是负数
        receivedTotalAmount =
          row.receivedTotalAmount * 1 > 0
            ? row.receivedTotalAmount * -1
            : row.receivedTotalAmount * 1
      } else {
        receivedTotalAmount = row.receivedTotalAmount * 1
      }
      const uncollected = totalAmount - receivedTotalAmount
      return uncollected.toFixed(2)
    },
    handleSummary(param) {
      const { columns, data } = param
      const sums = []
      let taxAmount = 0
      let saleAmount = 0
      let taxableAmount = 0
      let totalAmount = 0
      let receivedTotalAmount = 0
      data.forEach((item) => {
        taxAmount += item?.taxAmount || 0
        saleAmount += item?.saleAmount || 0
        taxableAmount += item?.taxableAmount || 0
        totalAmount += item?.totalAmount || 0
        receivedTotalAmount += item?.receivedTotalAmount || 0
      })
      let colNum = 0 // 因为数电跟电子发票没有打印状态，所以合计的列也要调整
      if (this.invoiceTypeNow.media !== 1) {
        colNum += -1
      }
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }
        if (index === 6 + colNum) {
          saleAmount = (Math.round(saleAmount * 100) / 100).toFixed(2)
          sums[index] = comma(saleAmount * 1, 2)
        }
        if (index === 7 + colNum) {
          taxAmount = (Math.round(taxAmount * 100) / 100).toFixed(2)
          sums[index] = comma(taxAmount * 1, 2)
        }
        if (index === 8 + colNum) {
          taxableAmount = (Math.round(taxableAmount * 100) / 100).toFixed(2)
          sums[index] = comma(taxableAmount * 1, 2)
        }
        if (index === 9 + colNum) {
          totalAmount = (Math.round(totalAmount * 100) / 100).toFixed(2)
          sums[index] = comma(totalAmount * 1, 2)
        }
        if (index === 10 + colNum) {
          receivedTotalAmount = (
            Math.round(receivedTotalAmount * 100) / 100
          ).toFixed(2)
          sums[index] = comma(receivedTotalAmount * 1, 2)
        }
        if (index === 11 + colNum) {
          let uncollectedAmount = totalAmount - receivedTotalAmount
          uncollectedAmount = (
            Math.round(uncollectedAmount * 100) / 100
          ).toFixed(2)
          sums[index] = comma(uncollectedAmount * 1, 2)
        }
      })
      return sums
    },
    handleLi(index) {
      this.nowSelectLeft = index
      this.form.invoiceTypeId = this.leftList[this.nowSelectLeft].id
      this.invoiceTypeNow = this.leftList[this.nowSelectLeft]
      this.nowRowData = []
      this.nowRowDatac = {}
      this.getList()
    },
    async getOptions() {
      try {
        const data1 = await loadTypeList()
        this.leftList = data1.data
        this.invoiceTypeNow = this.leftList[this.nowSelectLeft]
        this.form.invoiceTypeId = this.leftList[this.nowSelectLeft].id
        const data2 = await getIncomeType()

        this.optionsCascader = data2.data
        this.getList()
      } catch (error) {
        console.log(error)
      }
    },
    getList() {
      if (this.invoiceTypeNow.media === 2) {
        // 电子发票要去掉打印状态列
        for (let i = 0; i < this.tableColumns.length; i++) {
          if (this.tableColumns[i].prop === 'printState') {
            this.tableColumns.splice(i, 1)
            break
          }
        }
      } else if (this.invoiceTypeNow.media === 1) {
        // 纸质发票要插入打印状态列
        if (!this.tableColumns.some((item) => item.prop === 'printState')) {
          this.tableColumns.splice(1, 0, this.printColumn)
        }
      } else {
        // 全电发票不需要打印状态列
        for (let i = 0; i < this.tableColumns.length; i++) {
          if (this.tableColumns[i].prop === 'printState') {
            this.tableColumns.splice(i, 1)
            break
          }
        }
      }
      const params = {
        orgId: this.areaLogin.id,
        ...this.form
      }
      this.loading = true
      invoiceDetailPage(params)
        .then((res) => {
          this.table = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    /* table选择行 */
    handleSelectionChange(val) {
      this.nowRowData = val
    },
    /* 搜索 */
    handleSearch() {
      this.getList()
    },
    handlePackUp() {
      this.$refs['_advanceFormRef'].resetFields()
      this.advanceVisible = false
    },
    // 高级搜索出现与隐藏
    handleShowHide() {
      this.advanceVisible = true
    },
    handleRresetForm() {
      this.form = {
        drawer: '',
        advSummary: '',
        partCollection: '',
        invoiceTypeId: this.leftList[this.nowSelectLeft].id
      }
    },
    handleAdvanceSearch() {
      this.getList()
      this.advanceVisible = false
    },
    /* 查看 */
    handleView(row) {
      // if (row.invoiceState !== 1) {
      //   return this.$message.warning('发票状态为开出的才允许查看')
      // }
      if (row.invoiceState === 3) {
        return this.$message.warning('发票状态为遗失的不允许查看')
      }
      this.invoiceTypeSelect = this.leftList[this.nowSelectLeft]
      this.statusType = 'view'
      this.nowRowDatac = row
      if (this.invoiceTypeSelect.media === 3) {
        this.digitalDetailsVisible = true
      } else {
        this.detailsVisible = true
      }
    },
    /* 编辑 */
    handleEdit(row) {
      if (row.invoiceMedia === 3) {
        if (row.invoiceState > 0) {
          return this.$message.warning('已开出的发票不允许编辑')
        }
      } else {
        if (row.dockState === 1) {
          return this.$message.warning('发送到税控的不允许编辑')
        }
      }
      if (row.offsetType !== 0) {
        return this.$message.warning('红冲发票不允许编辑')
      }
      this.invoiceTypeSelect = this.leftList[this.nowSelectLeft]
      this.statusType = 'edit'
      this.nowRowDatac = row
      if (this.invoiceTypeSelect.media === 3) {
        // this.handleDigitalInvoicing()
        this.digitalDetailsVisible = true
      } else {
        this.detailsVisible = true
      }
    },
    /* 删除 */
    handleDelete(row) {
      if (row.invoiceMedia === 3) {
        if (row.invoiceState > 0) {
          return this.$message.warning('已开出的发票不允许删除')
        }
      } else {
        if (row.dockState === 1) {
          return this.$message.warning('发送到税控的不允许删除')
        }
      }
      this.$confirm('是否确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteInvoice({ id: row.id })
          .then((res) => {
            this.$message({
              type: 'success',
              message: res.message
            })
            this.getList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    /* 开票 */
    handleAdd() {
      if (!this.leftList[this.nowSelectLeft]) {
        return this.$message.warning('请先选择左边发票类型')
      }
      this.nowRowDatac = {}
      this.invoiceTypeSelect = this.leftList[this.nowSelectLeft]
      this.nowRowDatac = { offsetType: 0 }
      this.statusType = 'add'
      if (this.invoiceTypeSelect.media === 3) {
        // this.handleDigitalInvoicing()
        this.digitalDetailsVisible = true
      } else {
        this.detailsVisible = true
      }
    },
    /* 补开票 */
    handleSupply() {
      if (!this.leftList[this.nowSelectLeft]) {
        return this.$message.warning('请先选择左边发票类型')
      }
      this.nowRowDatac = {}
      this.invoiceTypeSelect = this.leftList[this.nowSelectLeft]
      this.nowRowDatac = { offsetType: 0 }
      this.statusType = 'supply'
      if (this.invoiceTypeSelect.media === 3) {
        // this.handleDigitalInvoicing()
        this.digitalDetailsVisible = true
      } else {
        this.detailsVisible = true
      }
    },
    /** 更新发票开出状态 */
    async handleRefreshState() {
      if (this.nowRowData.length === 0) {
        return this.$message.warning('请选择数据')
      }
      if (!this.nowRowData.every((item) => item.dockState === 1)) {
        return this.$message.warning('请选择已对接税务的发票')
      }
      if (!this.nowRowData.every((item) => item.invoiceState < 1)) {
        return this.$message.warning('请选择未开出成功的发票')
      }
      this.loading = true
      try {
        // 数电发票要先判断有没有注册订购并且电子税务局账号在登录有效期间
        const res = await getRegistryByOrg(this.areaLogin.id)
        if (res.returnCode !== '0') {
          return this.$message.error(res.message)
        }
        this.registry = res.data
        const swj = await getSwjLoginStatus(
          this.registry.registryType,
          this.registry.spId,
          this.registry.taxpayerNo
        )
        if (swj.returnCode !== '0') {
          return this.$message.error(swj.message)
        }
        if ((swj.needLoginSwj || '') === 'T') {
          // 要先登录电子税务局
          this.loginSwjDialogVisible = true
          return
        }
        if ((swj.needFaceScan || '') === 'Y') {
          // 需要重新扫脸，弹出扫脸弹窗
          this.scanDialogVisible = true
        } else {
          this.refreshDigitalState()
        }
      } catch (error) {
        console.log('handleAddInvoiceError:', error)
      } finally {
        this.loading = false
      }
    },
    refreshDigitalState() {
      // 用逗号拼接nowRowData.id
      const params = {
        ids: this.nowRowData.map((item) => item.id).join(',')
      }
      this.loading = true
      refreshInvoiceState(params)
        .then((res) => {
          this.$message.success(res.message)
        })
        .finally(() => {
          this.loading = false
          this.getList()
        })
    },
    // 数电发票发送到税务
    async handleSendDigital() {
      if (this.nowRowData.length !== 1) {
        return this.$message.warning('请选择一条数据')
      }
      if (this.nowRowData[0].invoiceState > 0) {
        return this.$message.warning('该发票已开出')
      }
      this.loading = true
      try {
        // 数电发票要先判断有没有注册订购并且电子税务局账号在登录有效期间
        const res = await getRegistryByOrg(this.areaLogin.id)
        if (res.returnCode !== '0') {
          return this.$message.error(res.message)
        }
        this.registry = res.data
        const swj = await getSwjLoginStatus(
          this.registry.registryType,
          this.registry.spId,
          this.registry.taxpayerNo
        )
        if (swj.returnCode !== '0') {
          return this.$message.error(swj.message)
        }
        if ((swj.needLoginSwj || '') === 'T') {
          // 要先登录电子税务局
          this.loginSwjDialogVisible = true
          return
        }
        if ((swj.needFaceScan || '') === 'Y') {
          // 需要重新扫脸，弹出扫脸弹窗
          this.scanDialogVisible = true
        } else {
          this.sendDigitalData()
        }
      } catch (error) {
        console.log('handleAddInvoiceError:', error)
      } finally {
        this.loading = false
      }
    },
    sendDigitalData() {
      const params = {
        id: this.nowRowData[0].id
      }
      this.loading = true
      sendDigital(params)
        .then((res) => {
          this.$message.success(res.message)
        })
        .finally(() => {
          this.loading = false
          this.getList()
        })
    },
    /* 发送到税控 */
    handleSend() {
      if (this.nowRowData.length <= 0) {
        return this.$message.warning('请选择一条数据')
      }
      if (this.nowRowData.length > 1) {
        return this.$message.warning('只能选择一条数据')
      }
      const params = {
        id: this.nowRowData[0].id
      }
      this.loading = true
      sendTaxControl(params)
        .then((res) => {
          this.$message.success(res.message)
        })
        .finally(() => {
          this.loading = false
          this.getList()
        })
    },
    // 打印提示
    printMsg(type = 'success', msg = '打印成功') {
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        type: type,
        showCancelButton: false
      })
        .then(() => {})
        .catch(() => {})
    },
    /* 套打 */
    async hanldeHit() {
      if (this.nowRowData.length <= 0) {
        return this.$message.warning('请选择一条数据')
      }
      if (this.nowRowData.length > 1) {
        return this.$message.warning('只能选择一条数据')
      }
      if (this.nowRowData[0]?.dockState === 0) {
        return this.$message.warning('已发送到税控才允许打印')
      }
      if (this.nowRowData[0].printState !== 0) {
        return this.$message.warning('已打印不能继续套打')
      }
      if (this.nowRowData[0].invoiceState !== 1) {
        return this.$message.warning('作废、遗失的不能继续套打')
      }
      try {
        const row = this.nowRowData[0]
        const dataArray = await Promise.all([
          this.loadPrintStyle(row.invoiceTypeId),
          this.loadPrintData(row.id)
        ])
        const printStyle = dataArray[0]
        const printData = dataArray[1]
        const printTime = await this.printPreview(printStyle, printData)
        if (printTime > 0) {
          this.updatePrintState(row.id, 1)
          return
        }
        this.printMsg('error', '打印失败')
      } catch (error) {
        console.log(error)
      } finally {
        this.getList()
      }
    },
    /* 补打 */
    async handlePrint() {
      if (this.nowRowData.length <= 0) {
        return this.$message.warning('请选择一条数据')
      }
      if (this.nowRowData.length > 1) {
        return this.$message.warning('只能选择一条数据')
      }
      if (this.nowRowData[0].printState === 0) {
        return this.$message.warning('还未打印不能进行补打')
      }
      if (this.nowRowData[0].invoiceState !== 1) {
        return this.$message.warning('作废、遗失的不能继续补打')
      }
      try {
        const row = this.nowRowData[0]
        const dataArray = await Promise.all([
          this.loadPrintStyle(row.invoiceTypeId),
          this.loadPrintData(row.id)
        ])
        const printStyle = dataArray[0]
        const printData = dataArray[1]
        const printTime = await this.printPreview(printStyle, printData)
        if (printTime > 0) {
          this.updatePrintState(row.id, 1, 1)
          // return
        }
        // this.printMsg('error', '打印失败')
      } catch (error) {
        console.log(error)
      } finally {
        this.getList()
      }
    },
    // 获取打印数据
    async loadPrintData(id) {
      return new Promise((resolve, reject) => {
        getPrintData({ detailId: id }).then((ret) => {
          if (ret.returnCode == '0' && ret.data) {
            resolve(ret.data)
          } else {
            reject(this.$message.warning(this.$t('获取打印数据失败')))
          }
        })
      })
    },
    // 获取打印数据
    async loadRePrintData(id) {
      return new Promise((resolve, reject) => {
        getInvoiceById({ id: id }).then((ret) => {
          if (ret.returnCode == '0' && ret.data) {
            resolve(ret.data)
          } else {
            reject(this.$message.warning(this.$t('获取打印数据失败')))
          }
        })
      })
    },
    // 获取打印样式
    loadPrintStyle(invoiceTypeId) {
      const vm = this
      return new Promise(async function (resolve, reject) {
        const res = await getbillPrintStyle({
          templateId: null,
          invoiceTypeId: invoiceTypeId,
          isReplaceContent: true
        })
        if (res.returnCode == '0' && res.data) {
          resolve(res.data)
        } else {
          reject(vm.$message.error(vm.$t('获取打印样式失败！')))
        }
      })
    },
    // 启动打印窗口
    async printPreview(printStyle, printData) {
      const { data } = await getDictionary('/financial/invoice/taxRate/')
      const taxRateOptions = data || []
      const printDataArray = []
      printDataArray.push(printData)
      return new Promise(function (resolve, reject) {
        const initFail = openPrintDesign(
          function (printTime) {
            resolve(printTime)
          },
          printStyle,
          printDataArray,
          taxRateOptions
        )
        if (initFail) {
          reject(this.$t('打印组件初始化失败'))
        }
      })
    },
    // 更新打印状态
    async updatePrintState(detailId, printState, reprintState = 0) {
      try {
        if (reprintState) {
          const params = {
            detailId: this.nowRowData[0].id
          }
          const results = await reprint(params)
          this.printMsg(
            results?.returnCode === '0' ? 'success' : 'error',
            results?.returnCode === '0' ? '打印成功' : '打印失败'
          )
          this.getList()
        } else {
          const params = {
            detailId,
            printState
          }
          const results = await updatePrintState(params)
          this.printMsg(
            results?.returnCode === '0' ? 'success' : 'error',
            results?.returnCode === '0' ? '打印成功' : '打印失败'
          )
          this.getList()
        }
      } catch (error) {
        console.error(error)
      }
    },
    /* 作废 */
    handleVoided() {
      if (this.nowRowData.length <= 0) {
        return this.$message.warning('请选择一条数据')
      }
      if (this.nowRowData.length > 1) {
        return this.$message.warning('只能选择一条数据')
      }
      if (this.nowRowData[0]?.invoiceState === 2) {
        return this.$message.warning('已作废不能再作废')
      }
      if (this.nowRowData[0]?.dockState === 0) {
        return this.$message.warning('已发送到税控才允许作废')
      }
      if (this.nowRowData[0]?.invoiceMedia === 2) {
        return this.$message.warning('电子发票不支持作废，请通过红冲方式操作!')
      }
      this.nowRowDatac = this.nowRowData[0]
      this.voidedVisible = true
    },
    /* 遗失 */
    handleLost() {
      this.invoiceTypeSelect = this.leftList[this.nowSelectLeft]
      this.lostVisible = true
    },
    /* 红冲 */
    handleOffset() {
      if (this.nowRowData.length !== 1) {
        return this.$message.warning('请选择一条数据')
      }
      this.nowRowDatac = this.nowRowData[0]
      if (this.nowRowDatac.invoiceState < 1) {
        return this.$message.warning('请选择发票状态为已开出的发票')
      }
      if (this.nowRowDatac.invoiceState === 2) {
        return this.$message.warning('已作废不能再红冲')
      }
      if (this.nowRowDatac.invoiceState === 3) {
        return this.$message.warning('已遗失不能再红冲')
      }
      if (this.nowRowDatac.offsetType !== 0) {
        return this.$message.warning('已红冲不能再次红冲')
      }
      if (
        this.nowRowDatac.invoiceMedia !== 3 &&
        this.nowRowDatac.dockState === 0
      ) {
        return this.$message.warning('发送到税控后才能红冲')
      }
      this.statusType = 'red'
      this.invoiceTypeSelect = this.leftList[this.nowSelectLeft]

      if (
        this.nowRowDatac.invoiceMedia !== 3 &&
        this.nowRowDatac.invoiceKind.toString() === '1'
      ) {
        this.$prompt('请输入通知单号：', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.+$/,
          inputErrorMessage: '请输入内容'
        })
          .then(({ value }) => {
            this.noticeCode = value
            this.detailsVisible = true
          })
          .catch(() => {
            this.$refs['_geverTableRef'].clearSelection()
          })
      } else {
        this.noticeCode = ''
        if (this.invoiceTypeSelect.media === 3) {
          // this.handleDigitalInvoicing()
          this.digitalDetailsVisible = true
        } else {
          this.detailsVisible = true
        }
      }
    },
    /* 下载发票 */
    handleDownloads() {
      if (this.nowRowData.length !== 1) {
        return this.$message.warning('请选择一条数据')
      }
      if (this.nowRowData[0].invoiceMedia === 3) {
        if (this.nowRowData[0].invoiceState <= 0) {
          return this.$message.warning('该发票还未开出成功')
        }
        window.location.href = this.nowRowData[0].invoiceUrl
      } else {
        if (this.nowRowData[0].dockState === 0) {
          return this.$message.warning('发送到税控后才能下载发票')
        }
        this.loading = true
        getInvoiceURL({ id: this.nowRowData[0].id })
          .then((res) => {
            const url = res.message
            const windowReference = window.open()
            windowReference.location.href = url
            // this.download(url, '发票')
            this.getList()
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    download(url, filename) {
      this.getBlob(url, (blob) => {
        this.saveAs(blob, filename)
      })
    },
    getBlob(url, cb) {
      const xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = function () {
        if (xhr.status === 200) {
          cb(xhr.response)
        }
      }
      xhr.send()
    },
    saveAs(blob, filename) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename)
      } else {
        const link = document.createElement('a')
        const body = document.querySelector('body')
        link.href = window.URL.createObjectURL(blob)
        link.download = filename
        // fix Firefox
        link.style.display = 'none'
        body.appendChild(link)
        link.click()
        body.removeChild(link)
        window.URL.revokeObjectURL(link.href)
      }
    },
    // 编辑开票方式，转先收后开
    updateBillInvoicingStage() {
      if (this.nowRowData.length !== 1) {
        return this.$message.warning('请选择一条需要调整的数据')
      }
      if (this.nowRowData[0].invoicingStage === 2) {
        this.$confirm('是否确认编辑开票方式', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateInvoicingStage({ detailId: this.nowRowData[0].id }).then(
            (res) => {
              this.$message.success(res.message)
              this.getList()
            }
          )
        })
      } else {
        this.$message.warning('此功能只开放开票方式为先开后收！')
      }
    },
    // 编辑开票方式，转先开后收
    updateBillInvoicingStage2() {
      if (!this.nowRowData || this.nowRowData.length === 0) {
        return this.$message.warning('请选择需要调整的数据')
      }
      const selectIdArr = []
      for (const selected of this.nowRowData) {
        if (selected.invoicingStage !== 1) {
          return this.$message.warning('此功能只开放开票方式先收后开！')
        }
        if (selected.offsetType > 0) {
          return this.$message.warning('红冲发票不允许编辑开票方式！')
        }
        if (selected.invoiceState !== 1) {
          return this.$message.warning('非正常开出的发票不允许编辑开票方式！')
        }
        if (selected.receivedTotalAmount > 0) {
          return this.$message.warning(
            '发票号' + selected.invoiceNo + '已被关联收款，请撤销收款后再操作！'
          )
        }
        selectIdArr.push(selected.id)
      }
      this.$confirm('是否确认编辑开票方式为先开后收？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateInvoicingStage2({ detailId: selectIdArr.join(',') }).then(
          (res) => {
            this.$message.success(res.message)
            this.getList()
          }
        )
      })
    },
    /* 修改通知单号 */
    handleEditNotice() {
      if (this.nowRowData.length <= 0) {
        return this.$message.warning('请选择一条数据')
      }
      if (this.nowRowData.length > 1) {
        return this.$message.warning('只能选择一条数据')
      }
      if (this.nowRowData[0].invoiceKind !== 1) {
        return this.$message.warning('非专用发票不能修改通知单号')
      }
      if (this.nowRowData[0].invoiceState === 2) {
        return this.$message.warning('已作废不能修改通知单号')
      }
      if (this.nowRowData[0].invoiceState === 3) {
        return this.$message.warning('已遗失不能修改通知单号')
      }
      if (this.nowRowData[0].offsetType !== 1) {
        return this.$message.warning('非红冲票不能修改通知单号')
      }
      if (this.nowRowData[0].dockState !== 0) {
        return this.$message.warning('已发送到税控不能修改通知单号')
      }
      this.nowRowDatac = this.nowRowData[0]
      this.$prompt('请输入通知单号：', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.+$/,
        inputErrorMessage: '请输入内容'
      })
        .then(({ value }) => {
          const params = {
            noticeCode: value,
            invoiceId: this.nowRowDatac.id
          }
          updateNoticeCode(params).then((res) => {
            this.$message.success(res.message)
            this.getList()
          })
        })
        .catch(() => {
          this.$refs['_geverTableRef'].clearSelection()
        })
    },
    handleRelateCashier() {
      if (this.nowRowData.length === 0) {
        return this.$message.warning('请选择需要关联的数据')
      }
      let selectedAmount = 0
      for (const selected of this.nowRowData) {
        if (selected.invoiceState !== 1) {
          return this.$message.warning('请选择发票状态为已开出的发票')
        }
        if (selected.invoiceMedia === 1 && selected.printState === 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 还未打印！'
          )
        }
        if (selected.offsetType > 0) {
          return this.$message.warning('红冲业务相关的发票请关联到会计凭证！')
        }
        if (selected.invoicingStage === 3) {
          return this.$message.warning('补开的发票请关联到会计凭证！')
        }
        if (selected.verifyState > 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 已核销！'
          )
        }
        if (selected.isRecorded > 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 已记账！'
          )
        }
        if (selected.receivedTotalAmount > 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 已被关联！'
          )
        }
        selectedAmount += Number(selected.totalAmount)
        // 在关联之前，先判断（除补开外，并且非红冲的）票据有没有被关联支出申请做收支相抵的
        if (selected.isRelatedPayment && selected.isRelatedPayment === 1) {
          return this.$message.warning(
            '发票号 ' +
              selected.invoiceNo +
              ' 已被关联支出申请，只能关联到会计凭证！'
          )
        }
      }
      this.selectedTotalAmount = selectedAmount
      this.selectedInvoiceIds = this.nowRowData.map(({ id }) => id).join(',')
      this.relatePageFlag = 'edit'
      // 把所选择的票据id跟票据金额合计传过去
      this.relateCashierVisible = true
    },
    cancelRelateCashier() {
      if (this.nowRowData.length === 0) {
        return this.$message.warning('请选择需要取消关联的数据')
      }
      for (const selected of this.nowRowData) {
        if (selected.isRecorded === 0) {
          return this.$message.warning('选择的发票还未记账！')
        }
        if (selected.isRecorded !== 2) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 未关联出纳账！'
          )
        }
        if (selected.verifyState > 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 已核销！'
          )
        }
      }
      this.selectedInvoiceIds = this.nowRowData.map(({ id }) => id).join(',')
      this.relatePageFlag = 'view'
      // 把所选择的票据id跟票据金额合计传过去
      this.relateCashierVisible = true
    },
    handleRelateAccounting() {
      if (this.nowRowData.length === 0) {
        return this.$message.warning('请选择需要关联的数据')
      }
      let selectedAmount = 0
      for (const selected of this.nowRowData) {
        if (selected.invoiceState !== 1) {
          return this.$message.warning('请选择发票状态为已开出的发票')
        }
        if (selected.invoiceMedia === 1 && selected.printState === 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 还未打印！'
          )
        }
        if (selected.verifyState > 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 已核销！'
          )
        }
        if (selected.isRecorded > 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 已记账！'
          )
        }
        selectedAmount += Number(selected.totalAmount)
        // 在关联之前，先判断（除补开外，并且非红冲的）票据有没有被关联支出申请做收支相抵的
        if (selected.isRelatedPayment && selected.isRelatedPayment === 1) {
          if (!selected.deductState || selected.deductState === 0) {
            return this.$message.warning(
              '发票号 ' +
                selected.invoiceNo +
                ' 已被关联收支相抵，请先冲销确认！'
            )
          }
        }
      }
      this.selectedTotalAmount = selectedAmount
      this.selectedInvoiceIds = this.nowRowData.map(({ id }) => id).join(',')
      this.relatePageFlag = 'edit'
      // 把所选择的票据id跟票据金额合计传过去
      this.relateAccountingVisible = true
    },
    cancelRelateAccounting() {
      if (this.nowRowData.length === 0) {
        return this.$message.warning('请选择需要取消关联的数据')
      }
      for (const selected of this.nowRowData) {
        if (selected.isRecorded === 0) {
          return this.$message.warning('选择的发票还未记账！')
        }
        if (selected.isRecorded !== 1) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 未关联会计凭证！'
          )
        }
        if (selected.verifyState > 0) {
          return this.$message.warning(
            '发票号 ' + selected.invoiceNo + ' 已核销！'
          )
        }
      }
      this.selectedInvoiceIds = this.nowRowData.map(({ id }) => id).join(',')
      this.relatePageFlag = 'view'
      // 把所选择的票据id跟票据金额合计传过去
      this.relateAccountingVisible = true
    },
    handleSuccessLoginSwj() {
      // 关闭账号登录窗口
      this.loginSwjDialogVisible = false
    },
    handleSuccessScanCode() {
      this.scanDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-content {
  section,
  header,
  article,
  aside,
  h3,
  p {
    padding: 0;
    margin: 0;
    background-color: #fff;
  }
  header {
    justify-content: space-between;
    margin: 0 20px;
    .code {
      flex: 1;
      aside {
        padding-left: 10px;
        p {
          &:first-child {
            font-size: 26px;
            letter-spacing: 2px;
          }
          &:last-child {
            display: flex;
            flex-direction: column;
            margin-left: 16px;
          }
        }
      }
    }

    .title {
      flex: 1;
      .title {
        font-size: 26px;
        letter-spacing: 5px;
      }
      .double-line {
        letter-spacing: 15px;
        text-decoration-line: underline;
        text-decoration-style: double;
      }
    }
    .number {
      flex: 1;
      p {
        &:first-child {
          font-size: 26px;
          margin-bottom: 10px;
          .big {
            margin: 0 30px 0 15px;
          }
          .small {
            font-size: 14px;
          }
        }
        &:last-child {
          margin-top: 10px;
          margin-right: 10px;
          .date {
            margin-left: 20px;
          }
        }
      }
    }
  }
  .table-content {
    margin: 5px;
    border: solid 1px #a95212;
    .purchase,
    .sale {
      border-bottom: solid 1px #a95212;
      .buyer,
      .password,
      .remark {
        padding: 10px;
        width: 34px;
        display: flex;
        align-items: center;
        border-right: solid 1px #a95212;
      }
      .info {
        flex: 1;
        padding: 5px 10px;
        border-right: solid 1px #a95212;
        li {
          line-height: 26px;
        }
      }
      .pw,
      .rm {
        width: 332px;
        padding: 10px;
        letter-spacing: 5px;
      }
    }
    .goods {
      li {
        padding: 5px 10px;
        border-right: solid 1px #a95212;
        &:last-child {
          border-right: none;
        }
      }
      .name {
        width: 310px;
      }
      .model {
        flex: 3;
      }
      .unit {
        flex: 1;
      }
      .quantity {
        flex: 2;
      }
      .price {
        flex: 3;
      }
      .money {
        flex: 3;
      }
      .tax-rate {
        flex: 1;
      }
      .tax-amount {
        flex: 3;
      }
      .count {
        letter-spacing: 80px;
      }
    }
    .money-count {
      border-top: solid 1px #a95212;
      height: 40px;
      line-height: 40px;
      .count {
        width: 310px;
        border-right: solid 1px #a95212;
      }
      .uppercase {
        flex: 1;
        justify-content: space-between;
        padding: 0 80px 0 30px;
      }
    }
    .sale {
      border-bottom: none;
      border-top: solid 1px #a95212;
    }
  }
  .footer {
    margin: 0 20px;
    padding: 10px 0 30px;
    li {
      flex: 1;
      font-size: 16px;
      letter-spacing: 5px;
    }
  }
  .ls3 {
    letter-spacing: 3px;
  }
  .font-d {
    color: #000;
  }
  .font-y {
    color: #a95212;
  }
  .font-b {
    color: #0062bd;
  }

  .df {
    display: flex;
  }
  .tr {
    text-align: right;
  }
  .tl {
    text-align: left;
  }
  .tc {
    text-align: center;
  }
  .header-invoice {
    width: 780px;
    justify-content: space-between;
  }
  .pr20 {
    padding-right: 20px;
  }
  .pb10 {
    padding-bottom: 10px;
  }
  .right-date {
    text-align: left;
    padding-top: 20px;
  }
}
.btn-box {
  display: inline-block;
  text-align: right;
  margin-bottom: 10px;
}
.function-btn {
  display: flex;
  div {
    float: left;
    padding-right: 8px;
    padding-bottom: 8px;
  }
}
.left-box {
  ul {
    width: 100%;
    height: 100%;
    li {
      width: 100%;
      text-align: center;
      line-height: 50px;
      font-size: 14px;
      border-bottom: 1px solid #cccccc88;
    }
    .active {
      background-color: #e8f4ff;
      color: #1890ff;
    }
  }
}

::v-deep .gever-table-pagination {
  margin-top: 3px !important;
}

.w100 {
  width: 100px;
}
.w120 {
  width: 120px;
}

.copyable-text {
  user-select: text; /* 允许选中文本 */
  -webkit-user-select: text; /* 兼容 Safari */
}
</style>
