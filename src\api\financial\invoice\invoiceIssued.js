/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-10-09 16:59:46
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2024-01-09 14:47:41
 */
import request from '@/utils/request'

/*
export function demo(data) {
  return request({
    url: ``,
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}
*/

/*
export function demo(params) {
  return request({
    url: ``,
    method: 'get',
    params,
    // payload: true,
  })
}
*/

/* 库存列表 */
export function page(data) {
  return request({
    url: '/invoice/invoiceStock/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}
/* 获取树形收入类型 */
export function getIncomeType(params) {
  return request({
    url: `/invoice/invoiceDetail/getIncomeType`,
    method: 'get',
    params,
    // payload: true,
  })
}
/* 列表 */
export function invoiceDetailPage(data) {
  return request({
    url: '/invoice/invoiceDetail/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}
/* 查看 */
export function getInvoiceById(params) {
  return request({
    url: `/invoice/invoiceDetail/getInvoiceById`,
    method: 'get',
    params
  })
}
/* 获取打印数据 */
export function getPrintData(params) {
  return request({
    url: `/invoice/invoiceDetail/getPrintData`,
    method: 'post',
    params
  })
}
/* 保存 */
export function save(data) {
  return request({
    url: '/invoice/invoiceDetail/save',
    method: 'post',
    data: data,
    payload: true,
    // withCredentials: true
  })
}

/* 删除发票 */
export function deleteInvoice(data) {
  return request({
    url: `/invoice/invoiceDetail/deleteInvoice`,
    method: 'post',
    data
  })
}

/* 数电发票发送到税务 */
export function sendDigital(data) {
  return request({
    url: `/invoice/invoiceDetail/sendDigital`,
    method: 'post',
    data
  })
}

/* 发送到税控 */
export function sendTaxControl(data) {
  return request({
    url: `/invoice/invoiceDetail/sendTaxControl`,
    method: 'post',
    data
  })
}

/* 发票作废 */
export function cancellation(data) {
  return request({
    url: `/invoice/invoiceDetail/cancellation`,
    method: 'post',
    data
  })
}

/* 发票遗失 */
export function invoiceLose(data) {
  return request({
    url: `/invoice/invoiceDetail/invoiceLose`,
    method: 'post',
    data
  })
}

/* 红冲 */
export function invoiceOffset(data) {
  return request({
    url: `/invoice/invoiceDetail/invoiceOffset`,
    method: 'post',
    data,
    payload: true,
  })
}

/* 发票类型列表 */
export function loadTypeList(data) {
  return request({
    url: `/invoice/invoiceType/loadTypeList`,
    method: 'get',
    data
  })
}

/* 根据机构ID查询收款方(进入开票页面之前需调用) */
export function getSellerByOrgId(params) {
  return request({
    url: `/invoice/invoiceSeller/getSellerByOrgId`,
    method: 'get',
    params
  })
}

/* 根据机构ID查询付款方信息 */
export function getPayerByOrgId(params) {
  return request({
    url: `/invoice/invoicePayer/getPayerByOrgId`,
    method: 'get',
    params
  })
}


/* 根据纳税人类别获取税收信息 */
export function selectByTaxpayerType(data) {
  return request({
    url: `/invoice/commonTaxpayerTypeTaxKind/selectByTaxpayerType`,
    method: 'post',
    data
  })
}

/* 套打 */
export function updatePrintState(data) {
  return request({
    url: `/invoice/invoiceDetail/updatePrintState`,
    method: 'post',
    data
  })
}

/* 补打 */
export function reprint(data) {
  return request({
    url: `/invoice/invoiceDetail/reprint`,
    method: 'post',
    data
  })
}

/* 获取发票下载链接 */
export function getInvoiceURL(params) {
  return request({
    url: `/invoice/invoiceDetail/getInvoiceURL`,
    method: 'get',
    params
  })
}

// 获取打印样式
export function getbillPrintStyle(data) {
  return request({
    url: `/invoice/invoiceDetail/loadPrintStyle?templateId=&invoiceTypeId=${data.invoiceTypeId}`,
    method: 'get',
    data
  })
}

//修改开票方式
export function updateInvoicingStage (data) {
  return request({
    url: `/invoice/invoiceDetail/updateInvoicingStage`,
    method: 'post',
    data
  })
}

//修改开票方式
export function updateInvoicingStage2 (data) {
  return request({
    url: `/invoice/invoiceDetail/updateInvoicingStage2`,
    method: 'post',
    data
  })
}

//修改通知单号
export function updateNoticeCode (data) {
  return request({
    url: `/invoice/invoiceDetail/updateNoticeCode`,
    method: 'post',
    data
  })
}

// 发票列表关联出纳收款
export function allCollection(data) {
  return request({
    url: `/invoice/invoiceDetail/allCollection`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 发票列表取消关联出纳 */
export function cancelRelateCashier(data) {
  return request({
    url: `/invoice/invoiceDetail/cancelRelateCashier`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 发票列表查询已关联的会计 */
export function getRelatedAccounting(data) {
  return request({
    url: `/invoice/invoiceDetail/getRelateAccounting`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 发票列表取消关联会计 */
export function cancelRelateAccounting(data) {
  return request({
    url: `/invoice/invoiceDetail/cancelRelateAccounting`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 更新开票状态 */
export function refreshInvoiceState(data) {
  return request({
    url: `/invoice/invoiceDetail/refreshInvoiceState`,
    method: 'post',
    data
  })
}
