<template>
  <gever-dialog
    :title="$t(`请选择${contractType === '0' ? '每期应收款' : '收款记录'}`)"
    :visible="dialogVisible"
    width="98%"
    :modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="dialog-box">
      <el-form
        ref="_contractFormRef"
        :inline="true"
        :model="form"
        :rules="rules"
      >
        <el-form-item :label="$t('合同标的物：')" prop="contractName">
          <el-input v-model="form.contractName" type="text" class="w100" />
        </el-form-item>
        <el-form-item :label="$t('合同编号：')" prop="contractCode">
          <el-input v-model="form.contractCode" type="text" class="w100" />
        </el-form-item>
        <el-form-item :label="$t('原合同编号：')" prop="contractCode">
          <el-input
            v-model="form.originalContractCode"
            type="text"
            class="w100"
          />
        </el-form-item>
        <el-form-item :label="$t('合同乙方：')" prop="partyB">
          <el-input v-model="form.partyB" type="text" class="w100" />
        </el-form-item>
        <el-form-item :label="$t('合同丙方：')" prop="partyC">
          <el-input v-model="form.partyC" type="text" class="w100" />
        </el-form-item>
        <el-form-item :label="$t('收款项目：')" prop="expenseName">
          <gever-select
            v-model="form.expenseName"
            path="/contract/collectionContract/expenseName/"
            class="w100"
          />
        </el-form-item>
        <el-form-item
          v-if="isRulesShould === '0'"
          :label="$t('时间范围：')"
          prop="timeRange"
        >
          <gever-select
            v-model="form.timeRange"
            :options="timeRangeOptions"
            :value-prop="'value'"
            style="width: 80px"
            @change="handleTimeRange"
          />
        </el-form-item>
        <el-form-item
          v-if="contractType === '0'"
          :label="$t('截止日期：')"
          prop="expiryDate"
        >
          <el-date-picker
            v-model="form.expiryDate"
            type="date"
            value-format="yyyy-MM-dd"
            class="w126"
            :placeholder="$t('选择日期')"
          />
        </el-form-item>
        <template v-else>
          <el-form-item :label="$t('开始日期：')" prop="receiveStartDate">
            <el-date-picker
              v-model="form.receiveStartDate"
              type="date"
              value-format="yyyy-MM-dd"
              class="w126"
              :placeholder="$t('选择日期')"
            />
          </el-form-item>
          <el-form-item :label="$t('结束日期：')" prop="receiveEndDate">
            <el-date-picker
              v-model="form.receiveEndDate"
              type="date"
              value-format="yyyy-MM-dd"
              class="w126"
              :placeholder="$t('选择日期')"
            />
          </el-form-item>
        </template>
        <div class="btn-box">
          <el-button type="default" round @click="handleReset">
            {{ $t('重置') }}
          </el-button>
          <el-button type="primary" round @click="handleSearch">
            {{ $t('搜索') }}
          </el-button>
        </div>
      </el-form>
      <gever-table
        ref="_geverTableRef"
        :columns="tableColumns"
        :data="table.rows"
        :total="table.total"
        highlight-current-row
        pagination
        show-summary
        :pagi="form"
        :page-sizes="[5, 10, 20, 30, 40, 50, 100, 200]"
        :summary-method="getSummaries"
        @pagination-change="getList"
        @selection-change="handleSelectionChange"
      >
        <!-- 收费期间 -->
        <template #period="{ row }">
          {{ $t(row.receivableStartDate + '-' + row.receivableEndDate) }}
        </template>
        <!-- 本期收款 -->
        <template #relAmount="{ row }">
          <gever-input-number
            v-if="table.rows"
            v-model="row.relAmount"
            :controls="false"
            :precision="2"
            :min="0.01"
            :max="
              row.relAmount.toString()
                ? (row.money || 0) - (row.billMoney || 0)
                : **********
            "
            class="alingRight"
            @blur="inputBlur"
          ></gever-input-number>
        </template>
        <template #money="{ row }">{{ comma(row.money) }}</template>
        <template #billMoney="{ row }">{{ comma(row.billMoney) }}</template>
        <template #manualMoney="{ row }">{{ comma(row.manualMoney) }}</template>
        <template #billReceivedMoney="{ row }">
          {{ comma(row.billReceivedMoney) }}
        </template>
        <template #totalMoney="{ row }">{{ comma(row.totalMoney) }}</template>
        <template #noInvoiceMoney="{ row }">
          {{ comma(row.receiveMoney - row.receiveBillMoney) }}
        </template>
        <template #currentInvoiceMoney="{ row }">
          <gever-input-number
            v-if="row.currentInvoiceMoney"
            v-model="row.currentInvoiceMoney"
            :controls="false"
            :precision="2"
            :min="0.01"
            :max="
              row.currentInvoiceMoney.toString()
                ? (row.receiveMoney || 0) - (row.receiveBillMoney || 0)
                : **********
            "
            class="alingRight"
            @blur="inputBlur"
          ></gever-input-number>
        </template>
      </gever-table>
    </div>
    <template #footer>
      <el-button @click="handleClose">{{ $t('取 消') }}</el-button>
      <el-button type="primary" @click="handleSelect">
        {{ $t('确 认') }}
      </el-button>
    </template>
  </gever-dialog>
</template>

<script>
import {
  getAllContractType,
  getInvoicableReceivableAccounts,
  getReceivabledNoBillAccounts
} from '@/api/financial/bills/billUseDetail.js'
import { selectByOrgCode } from '@/api/bill/keywords.js'
import { createNamespacedHelpers } from 'vuex'
import { formatMoney } from '@/utils/index.js'
const { mapState } = createNamespacedHelpers('area')
export default {
  props: {
    dialogVisible: { type: Boolean, default: false },
    contractType: { type: String, default: '0' },
    partyB: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      areaOptions: [],
      isRulesShould: '',
      form: {
        orgId: '',
        contractCode: '',
        originalContractCode: '',
        contractName: '',
        contractType: '',
        partyB: '',
        expenseName: '',
        timeRange: '1',
        expiryDate: '',
        receiveStartDate: '',
        receiveEndDate: '',
        page: 1,
        rows: 100
      },
      rules: {},
      rulesRecord: {
        receiveStartDate: [
          { required: true, message: '请选择时间', trigger: ['blur', 'change'] }
        ],
        receiveEndDate: [
          { required: true, message: '请选择时间', trigger: ['blur', 'change'] }
        ]
      },
      rulesShould: {
        expiryDate: [
          { required: true, message: '请选择时间', trigger: ['blur', 'change'] }
        ]
      },
      contractTypeOptions: [],
      timeRangeOptions: [
        { value: '1', text: '当月', selected: true },
        { value: '2', text: '半年' },
        { value: '3', text: '当年' }
      ],
      loading: false,
      tableColumns: [],
      // 每期应收款
      tableColumnsShould: [
        { type: 'selection', fixed: 'left', selectable: this.selectable },
        { prop: 'contractTypeName', label: '合同类型', minWidth: 100, align: 'left' },
        { prop: 'contractName', label: '合同标的物', minWidth: 100, align: 'left' },
        { prop: 'contractCode', label: '合同编号', minWidth: 160, align: 'left' },
        { prop: 'originalContractCode', label: '原合同编号', minWidth: 120, align: 'left' },
        { prop: 'expenseName', label: '收款项目', minWidth: 75, align: 'left' },
        { prop: 'period', label: '收费期间', minWidth: 165, align: 'center' },
        { prop: 'receivablesDate', label: '应收日期', minWidth: 95, align: 'center' },
        { prop: 'money', label: '应收金额', minWidth: 100, align: 'right' },
        { prop: 'billMoney', label: '已开票金额', minWidth: 100, align: 'right' },
        { prop: 'manualMoney', label: '已收款金额-手工', minWidth: 130, align: 'right' },
        { prop: 'billReceivedMoney', label: '已收款金额-开票', minWidth: 130, align: 'right' },
        { prop: 'relAmount', label: '本期收款', minWidth: 120, align: 'right' },
        { prop: 'secondName', label: '乙方名称', minWidth: 120, align: 'left' },
        { prop: 'totalMoney', label: '合同总金额', minWidth: 120, align: 'right' },
        { prop: 'contractStartDate', label: '合同开始日期', minWidth: 100, align: 'center' },
        { prop: 'contractEndDate', label: '合同结束日期', minWidth: 100, align: 'center' },
        { prop: 'contractStatus', label: '合同状态', convert: { options: [{ id: 0, text: '草稿' }, { id: 1, text: '履约中' }, { id: 2, text: '履约完成' }, { id: 3, text: '终止' }] }, minWidth: 85, align: 'center' }
      ],
      // 收款记录
      tableColumnsRecord: [
        { type: 'selection', fixed: 'left', selectable: this.selectable },
        { prop: 'contractTypeName', label: '合同类型', minWidth: 100, align: 'left' },
        { prop: 'contractName', label: '合同标的物', minWidth: 100, align: 'left' },
        { prop: 'contractCode', label: '合同编号', minWidth: 160, align: 'left' },
        { prop: 'originalContractCode', label: '原合同编号', minWidth: 120, align: 'left' },
        { prop: 'expenseName', label: '收款项目', minWidth: 75, align: 'left' },
        { prop: 'period', label: '收费期间', minWidth: 165, align: 'center' },
        { prop: 'receivablesDate', label: '应收日期', minWidth: 95, align: 'center' },
        { prop: 'money', label: '应收金额', minWidth: 100, align: 'right' },
        { prop: 'receiveMoney', label: '实收金额', minWidth: 100, align: 'right', filter: 'money' },
        { prop: 'receiveBillMoney', label: '收款已开票金额', minWidth: 116, align: 'right' },
        { prop: 'noInvoiceMoney', label: '收款待开票金额', minWidth: 116, align: 'right' },
        { prop: 'currentInvoiceMoney', label: '本次开票金额', minWidth: 125, align: 'right', filter: 'money' },
        { prop: 'secondName', label: '乙方名称', minWidth: 120, align: 'left' },
        { prop: 'totalMoney', label: '合同总金额', minWidth: 120, align: 'right' },
        { prop: 'contractStartDate', label: '合同开始日期', minWidth: 100, align: 'center' },
        { prop: 'contractEndDate', label: '合同结束日期', minWidth: 100, align: 'center' },
        { prop: 'contractStatus', label: '合同状态', convert: { options: [{ id: 0, text: '草稿' }, { id: 1, text: '履约中' }, { id: 2, text: '履约完成' }, { id: 3, text: '终止' }] }, minWidth: 85, align: 'center' }
      ],
      table: {
        rows: [],
        total: 0
      },
      nowSelectData: []
    }
  },
  computed: {
    ...mapState(['areaLogin']),
    nowTime() {
      return (status) => {
        // 创建 Date 对象
        const date = new Date()
        // 获取年月日
        const year = date.getFullYear()
        const month = date.getMonth() + 1 // 月份从0开始，需要加1
        let day
        if (status === 'start') {
          day = 1
        } else if (status === 'end') {
          day = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
        } else {
          day = date.getDate()
        }
        // 格式化为 yyyy-mm-dd
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
      }
    },
    getLastTime() {
      return (status) => {
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear()
        let lastDayOfTime
        if (status === '2') {
          lastDayOfTime = new Date(currentYear, 6, 1)
        } else if (status === '3') {
          // 获取下一年的第一天
          const nextYearFirstDay = new Date(currentYear + 1, 0, 1)
          // 减去一天得到当年的最后一天
          lastDayOfTime = new Date(nextYearFirstDay - 1)
        } else {
          // 获取下个月的第一天
          const nextMonthFirstDay = new Date(
            currentYear,
            currentDate.getMonth() + 1,
            1
          )
          // 减去一天得到当前月的最后一天
          lastDayOfTime = new Date(nextMonthFirstDay - 1)
        }
        // 格式化日期为 "yyyy-mm-dd" 形式
        return lastDayOfTime.toISOString().slice(0, 10)
      }
    },
    selectRows() {
      return this.$refs['_geverTableRef'].selection
    },
    comma() {
      return (val, keep_dec = 2) => {
        if (val) {
          if (keep_dec) {
            val = Number(val).toFixed(keep_dec)
          }
          val = String(val)
          if (val.indexOf('.') === -1) {
            return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
          }
          return (
            val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
            '.' +
            val.split('.')[1]
          )
        } else {
          return '0.00'
        }
      }
    }
  },
  watch: {
    contractType: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal === '0') {
          this.tableColumns = this.tableColumnsShould
          this.rules = this.rulesShould
        } else {
          this.tableColumns = this.tableColumnsRecord
          this.rules = this.rulesRecord
        }
        this.isRulesShould = newVal
      }
    },
    partyB: {
      deep: true,
      immediate: true,
      handler(val) {
        this.$set(this.form, 'partyB', val)
      }
    }
  },
  created() {
  },
  mounted() {
    this.form.expiryDate = this.getLastTime(this.form.timeRange)
    this.form.receiveStartDate = this.nowTime('start')
    this.form.receiveEndDate = this.nowTime('end')
    this.getOptions()
    this.getList()
  },
  methods: {
    handleTimeRange(val) {
      this.form.expiryDate = this.getLastTime(val)
    },
    getOptions() {
      getAllContractType().then((res) => {
        this.contractTypeOptions = res.data
      })
    },
    getList() {
      this.form.orgId = this.areaLogin.id
      const params = {...this.form}
      this.loading = true
      if (this.contractType === '0') {
        getInvoicableReceivableAccounts(params)
          .then((res) => {
            this.table.rows = undefined
            this.table = res.data
            this.table.rows.forEach((item, index) => {
              // item.relAmount = item.money - item.billMoney
              this.$set(
                this.table.rows[index],
                'relAmount',
                item.money - item.billMoney - item.manualMoney
              )
            })
            this.$forceUpdate()
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        getReceivabledNoBillAccounts(params)
          .then((res) => {
            this.table = res.data
            this.table.rows.forEach((item, index) => {
              this.$set(
                this.table.rows[index],
                'currentInvoiceMoney',
                item.receiveMoney - item.receiveBillMoney
              )
            })
            this.$forceUpdate()
          })
          .finally(() => {
            this.loading = false
          })
      }
      selectByOrgCode({ orgCode: this.areaLogin.code })
        .then((res) => {
          this.areaOptions = res.data
        })
    },
    handleSearch() {
      this.$refs['_contractFormRef'].validate((valid) => {
        if (valid) {
          this.getList()
        }
      })
    },
    selectable(row) {
      if (this.areaOptions) {
        const status = this.areaOptions.findIndex((val) => {
          return row.expenseName
            .replace(/\s/g, '')
            .includes(val.keyword.replace(/\s/g, ''))
        })
        return status === -1
      }
      return true
    },
    handleReset() {
      this.$refs['_contractFormRef'].resetFields()
      this.form.partyC = ''
    },
    // 表尾合计
    getSummaries(param) {
      const sums = []
      sums[1] = '合计勾选'
      if (this.nowSelectData.length === 0) {
        sums[6] = '0 条'
        return sums
      }
      const { columns } = param
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计勾选'
          return
        }
        if (index === 6) {
          sums[index] = `${this.nowSelectData.length} 条`
          return
        }
        if (column.label?.includes('金额') || column.label === '本期收款') {
          const countAmount = this.nowSelectData.reduce((t, c) => {
            return (
              (Math.round(t * 100) / 100).toFixed(2) * 1 + c?.[column.property] || 0
            )
          }, 0)
          sums[index] = formatMoney(countAmount)
        }
      })
      return sums
    },
    /* table 选择复选框回调 */
    handleSelectionChange(data) {
      this.nowSelectData = data
    },
    // 金额失焦
    inputBlur(e, precision = 2) {
      const reg =
        /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/
      const value = e.target.value
      if (reg.test(value)) {
        e.target.value = (value * 1).toFixed(precision)
      } else {
        e.target.value = (0 * 1).toFixed(precision)
      }
    },
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    handleSelect() {
      if (JSON.stringify(this.nowSelectData) === '{}') {
        return this.$message.warning('请选择数据')
      }
      this.$emit('selectData', this.nowSelectData)
      this.$emit('update:dialogVisible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  margin-top: 5vh !important;
}

::v-deep .el-dialog__body {
  height: calc(100vh - 200px) !important;
  .dialog-box {
    height: 100%;
  }
}

::v-deep .alingRight {
  .el-input__inner {
    text-align: right;
  }
}

.btn-box {
  display: inline-block;
  text-align: right;
  margin-bottom: 10px;
}

.w100 {
  width: 126px;
}

.w126 {
  width: 126px;
}
</style>
