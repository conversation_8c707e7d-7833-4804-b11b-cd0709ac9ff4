<template>
  <div class="invoice-detail-xw-container">
    <fold-box
      v-if="areaLogin.level === 5"
      :left-title="$t('发票性质')"
      :right-title="$t('发票列表')"
    >
      <template #left>
        <div v-loading="loading" class="left-box">
          <ul>
            <li
              v-for="type in invoiceTypeOptions"
              :key="type.value"
              :class="{ active: invoiceType === type.value }"
              @click="handleInvoiceTypeChange(type.value)"
            >
              {{ type.label }}
            </li>
          </ul>
        </div>
      </template>
      <template #right>
        <!-- 右侧内容 -->
        <div v-loading="loading" class="right-box">
          <!-- 搜索条件 -->
          <el-form ref="searchForm" :model="searchParams" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="发票种类">
                  <el-select v-model="typeName" placeholder="请选择">
                    <el-option label="全部" value="0"></el-option>
                    <el-option
                      v-for="(label, value) in invoiceTypeNameOptions"
                      :key="value"
                      :label="`（${value}）${label}`"
                      :value="value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发票代码">
                  <el-input v-model="searchParams.invoiceCode" placeholder="请输入发票代码"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发票号码">
                  <el-input v-model="searchParams.invoiceNo" placeholder="请输入发票号码"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="开票日期">
                  <el-date-picker
                    v-model="searchParams.beginDrawDate"
                    type="date"
                    placeholder="选择开始日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="至">
                  <div class="date-and-btn">
                    <el-date-picker
                      v-model="searchParams.endDrawDate"
                      type="date"
                      placeholder="选择结束日期"
                      style="width: 150px; margin-right: 20px;"
                    ></el-date-picker>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                    <el-button type="primary" @click="getOutData">获取外部发票数据</el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <!-- 列表数据 -->
          <gever-table
            ref="_geverTableRef"
            :columns="tableColumns"
            :data="table.rows"
            highlight-current-row
            pagination
            :total="table.total"
            :pagi="searchParams"
            @pagination-change="fetchData"
            @selection-change="handlePageChange"
          >
            <template #operation="{ row }">
              <el-button
                type="text"
                @click="handleView(row)"
              >
                {{ $t('查看') }}
              </el-button>
            </template>
          </gever-table>
          <!-- 分页 -->
          <el-pagination
            layout="prev, pager, next"
            :page-size="pageSize"
            @current-change="handlePageChange"
          >
          </el-pagination>
        </div>
      </template>
    </fold-box>
    <!-- 数电开票 -->
    <InvoiceDetailView
      v-if="digitalDetailsVisible"
      :dialog-visible.sync="digitalDetailsVisible"
      :row-data="nowRowDatac"
      :org-id="areaLogin.id"
      @updateList="getList"
    >
    </InvoiceDetailView>
    <el-dialog
      :title="$t('获取外部发票数据')"
      :visible.sync="externalInvoiceDialogVisible"
      width="450px"
      style="min-height: 500px;"
    >
      <el-form
        ref="externalInvoiceForm"
        :model="externalInvoiceData"
        label-width="120px"
        :rules="externalInvoiceRules"
        :hide-required-asterisk="false"
        @submit.native.prevent="handleGetOutDataSubmit"
      >
        <el-form-item label="注册类型" prop="registerType">
          <el-select v-model="externalInvoiceData.registerType" placeholder="请选择注册类型" style="width: 150px;" disabled>
            <el-option
              v-for="item in registerTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="开始日期" prop="beginDate">
          <el-date-picker
            v-model="externalInvoiceData.beginDate"
            type="date"
            placeholder="选择开始日期"
          />
        </el-form-item>

        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="externalInvoiceData.endDate"
            type="date"
            placeholder="选择结束日期"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleGetOutDataClose">取 消</el-button>
        <el-button type="primary" round icon="el-icon-upload2" :loading="dialogLoading" @click="handleGetOutDataSubmit">
          {{ dialogLoading ? '获取数据中' : '确认' }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 登录电子税务局 -->
    <login-swj-dialog
      v-if="loginSwjDialogVisible"
      :dialog-visible.sync="loginSwjDialogVisible"
      :registry="registry"
      @successLoginSwj="handleSuccessLoginSwj"
    >
    </login-swj-dialog>
    <!-- 扫码认证 -->
    <qr-code-dialog
      v-if="scanDialogVisible"
      :dialog-visible.sync="scanDialogVisible"
      :registry="registry"
      @successScanCode="handleSuccessScanCode"
    ></qr-code-dialog>
  </div>
</template>
<script>
import {
  getRegistryByOrg,
  getSwjLoginStatus
} from '@/api/financial/invoice/invoiceRegistry'
import { page, getApiInvoiceDetail, getInvoiceType } from '@/api/financial/invoice/invoiceDetailXw.js'
import { createNamespacedHelpers } from 'vuex'
import InvoiceDetailView from '@/views/financial/invoice/invoiceDetailXw/components/digitalDetails.vue'
import { formatDate } from '@/utils/date'
import QrCodeDialog from '@/views/financial/invoice/invoiceIssued/components/qrCodeDialog.vue'
import LoginSwjDialog from '@/views/financial/invoice/invoiceIssued/components/loginSwjDialog.vue'

const { mapState } = createNamespacedHelpers('area')
export default {
  components: { LoginSwjDialog, QrCodeDialog, InvoiceDetailView },
  data() {
    return {
      lastQueryTime: null, // 新增：记录最后一次查询的时间戳
      dialogLoading: false, // 仅用于弹窗的 loading
      invoiceType: '0', // 默认选中销项发票
      loading: false,
      digitalDetailsVisible: false,
      nowRowDatac: null,
      invoiceTypeOptions: [
        { label: '销项发票', value: '0' },
        { label: '进项发票', value: '1' }
      ],
      typeName: '0', // 默认选中“全部”
      invoiceTypeNameOptions: {}, // 存储从后端获取的发票类型
      searchParams: {
        invoiceType: 0,
        typeName: '0', // 默认选中“全部”
        invoiceCode: '',
        invoiceNo: '',
        beginDrawDate: '',
        endDrawDate: '',
        orgId: this.areaLogin?.id
      },
      table: {
        rows: [],
        total: 0
      },
      tableColumns: [
        { prop: 'typeCode', label: '种类代码', width: 80 },
        { prop: 'typeName', label: '发票种类', width: 180, align: 'left' },
        { prop: 'invoiceCode', label: '发票代码', width: 150, align: 'left' },
        { prop: 'invoiceNo', label: '发票号码', width: 180, align: 'left' },
        { prop: 'drawDate', label: '开票日期', width: 150, align: 'left' },
        { prop: 'drawer', label: '开票人', width: 80 },
        { prop: 'payee', label: '收款人', width: 80 },
        { prop: 'reviewer', label: '复核人', width: 80 },
        { prop: 'saleAmount', label: '发票金额', width: 120 },
        { prop: 'taxAmount', label: '发票税额', width: 120 },
        { prop: 'taxableAmount', label: '应税金额', width: 120 },
        { prop: 'totalAmount', label: '价税合计', width: 120 },
        { prop: 'listFlag', label: '清单标记', width: 100 },
        { prop: 'offsetState', label: '红冲状态', width: 100,
          convert: {
            options: [
              { id: 0, text: '正常' },
              { id: 1, text: '红冲票' },
              { id: 2, text: '被红冲' }
            ]
          }
        },
        { prop: 'riskLevel', label: '分险等级', width: 100 },
        { prop: 'offsetInvoiceNo', label: '全电纸质发票号', width: 110, align: 'left' },
        { prop: 'machineCode', label: '信息表编码', width: 180, align: 'left' },
        { prop: 'offsetSourceNo', label: '原发票号', width: 180, align: 'left' },
        { prop: 'payerName', label: '购方名称', width: 250, align: 'left' },
        { prop: 'payerTaxpayerNo', label: '购方税号', width: 200, align: 'left' },
        { prop: 'payerContactInfo', label: '购方（地址、电话）', width: 400, align: 'left' },
        { prop: 'payerAccountInfo', label: '购方（开户行、账号）', width: 400, align: 'left' },
        { prop: 'sellerName', label: '销方名称', width: 250, align: 'left' },
        { prop: 'sellerTaxpayerNo', label: '销方税号', width: 180, align: 'left' },
        { prop: 'sellerContactInfo', label: '销方（地址、电话）', width: 400, align: 'left' },
        { prop: 'sellerAccountInfo', label: '销方（开户行、账号）', width: 400, align: 'left' },
        { prop: 'summary', label: '摘要', width: 250, align: 'left' },
        { prop: 'remark', label: '备注', width: 350, align: 'left' },
        { prop: 'createTime', label: '创建时间', width: 150 },
        { prop: 'modifiedTime', label: '修改时间', width: 150 },
        { slotName: 'operation', label: '操作', width: 120, fixed: 'right' }
      ],
      pageSize: 10,
      currentPage: 1,
      externalInvoiceDialogVisible: false, // 控制获取外部发票数据对话框显示
      externalInvoiceData: { // 存储获取外部发票数据表单数据
        registerType: '',
        beginDate: '',
        endDate: ''
      },
      registerTypeOptions: [ // 注册类型选项
        { label: '01 广州税航', value: '01' },
        { label: '02 上海皓逸', value: '02' }
      ],
      externalInvoiceRules: { // 表单验证规则
        registerType: [{ required: true, message: '请选择注册类型', trigger: 'change' }],
        beginDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
        endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }]
      },
      loginSwjDialogVisible: false, // 登录电子税务局账号
      scanDialogVisible: false, // 是否需要扫描认证
      registry: {} // 有效期内的注册订购信息
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler() {
        this.handleSuccessLoginSwj()
        this.handleSuccessScanCode()
        this.fetchData()
      }
    },
    invoiceType(newVal) {
      this.externalInvoiceData.invoiceType = newVal
    }
  },
  mounted() {
    if (this.areaLogin.level !== 5) {
      this.$message.warning('请选择机构')
    }
    this.fetchData()
  },
  created() {
    this.initExternalInvoiceData()
    this.loadInvoiceTypes()
  },
  methods: {
    initExternalInvoiceData() {
      this.externalInvoiceData = {
        invoiceType: this.invoiceType,
        orgId: this.areaLogin.id,
        registerType: '',
        beginDate: '',
        endDate: ''
      }
    },
    handleInvoiceTypeChange(value) {
      // 发票性质改变时重新获取数据
      this.invoiceType = value
      this.searchParams.invoiceType = this.invoiceType
      this.currentPage = 1
      this.fetchData()
    },
    search() {
      this.searchParams.invoiceType = this.invoiceType
      this.searchParams.typeName = this.typeName
      this.currentPage = 1
      this.fetchData()
    },
    resetSearch() {
      this.searchParams = {
        invoiceType: 0,
        typeName: '0',
        invoiceCode: '',
        invoiceNo: '',
        beginDrawDate: '',
        endDrawDate: '',
        orgId: this.areaLogin.id
      }

      this.invoiceType = 0
      this.currentPage = 1
      this.fetchData()
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchData()
    },
    fetchData() {
      this.searchParams.orgId = this.areaLogin.id
      const params = {
        ...this.searchParams

      }
      this.loading = true
      page(params)
        .then((res) => {
          this.table = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleView(row) {
      // 设置参数并打开窗口
      this.openDigitalDetailsDialog({
        rowData: row,
        orgId: this.areaLogin.id
      })
    },
    openDigitalDetailsDialog({ rowData, orgId }) {
      this.nowRowDatac = rowData
      this.orgId = orgId
      this.digitalDetailsVisible = true
    },
    async getOutData() {
      this.loading = true
      try {
        // 数电发票要先判断有没有注册订购并且电子税务局账号在登录有效期间
        const res = await getRegistryByOrg(this.areaLogin.id)
        if (res.returnCode !== '0') {
          return this.$message.error(res.message)
        }
        this.registry = res.data
        // 动态设置 registerTypeOptions 和 registerType 表单项
        this.externalInvoiceData.registerType = this.registry.registryType
        const swj = await getSwjLoginStatus(this.registry.registryType, this.registry.spId, this.registry.taxpayerNo)
        if (swj.returnCode !== '0') {
          return this.$message.error(swj.message)
        }
        if ((swj.needLoginSwj || '') === 'T') {
          // 要先登录电子税务局
          this.loginSwjDialogVisible = true
          return
        }
        if ((swj.needFaceScan || '') === 'Y') {
          // 需要重新扫脸，弹出扫脸弹窗
          this.scanDialogVisible = true
        } else {
          // 直接打开获取外部发票数据窗口
          this.externalInvoiceDialogVisible = true
        }
      } finally {
        this.loading = false
      }
    },
    async handleGetOutDataSubmit() {
      const now = new Date().getTime()
      const invoiceType = this.invoiceType // 获取当前发票类型
      const lastQueryKey = `lastQueryTime_${invoiceType}`
      const lastQueryTimeStr = sessionStorage.getItem(lastQueryKey)
      const lastQueryTime = lastQueryTimeStr ? parseInt(lastQueryTimeStr, 10) : null

      if (lastQueryTime && now - lastQueryTime < 3600000) {
        this.$message.warning('请等待一小时后再进行查询')
        return
      }

      const valid = await this.$refs.externalInvoiceForm.validate()
      if (valid) {
        // 验证通过，继续业务逻辑
        console.log('表单数据:', this.externalInvoiceData)
        // 调用API
        // 将开始日期和结束日期转换成'yyyy-MM-DD'
        this.externalInvoiceData.beginDate = formatDate(
          this.externalInvoiceData.beginDate,
          'yyyy-MM-DD'
        )
        this.externalInvoiceData.endDate = formatDate(
          this.externalInvoiceData.endDate,
          'yyyy-MM-DD'
        )
      } else {
        return this.$message.error('请填写必填项')
      }
      this.queryOutData(lastQueryKey, now)
    },
    queryOutData(lastQueryKey, now) {
      this.externalInvoiceData.invoiceType = this.invoiceType
      this.externalInvoiceData.orgId = this.areaLogin.id
      this.dialogLoading = true // 弹窗 loading 开启
      getApiInvoiceDetail(this.externalInvoiceData)
        .then(res => {
          this.$alert(res.data.msg, {
            dangerouslyUseHTMLString: true,
            showClose: false,
            showCancelButton: false
          }).then(() => {
            if (res.data.flag !== '0') {
              // 分别记录不同类型发票的查询时间
              sessionStorage.setItem(lastQueryKey, now.toString())
            }
            this.dialogLoading = false // 结束弹窗 loading
            this.handleGetOutDataClose()
            this.fetchData()// 数据同步成功后刷新主页面数据
          })
        })
        .catch(err => {
          this.dialogLoading = false // 结束弹窗 loading
          this.$message.error('获取发票数据失败: ' + err.message)
        })
    },
    handleGetOutDataClose() {
      // 对话框关闭时重置表单
      this.externalInvoiceDialogVisible = false
      this.$refs.externalInvoiceForm.resetFields()
      this.initExternalInvoiceData()
    },
    handleSuccessLoginSwj() {
      // 关闭账号登录窗口
      this.loginSwjDialogVisible = false
    },
    handleSuccessScanCode() {
      this.scanDialogVisible = false
    },
    async loadInvoiceTypes() {
      try {
        const response = await getInvoiceType() // 调用后端接口
        this.invoiceTypeNameOptions = response.data // 假设返回的数据结构是 { data: Map<String, String> }
      } catch (error) {
        console.error('获取发票类型失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-box {
  display: inline-block;
  text-align: right;
  margin-bottom: 10px;
}
.function-btn {
  padding: 2px 2px;
  div {
    float: left;
    padding-right: 8px;
    padding-bottom: 8px;
  }
}
.left-box {
  ul {
    width: 100%;
    height: 100%;
    li {
      width: 100%;
      text-align: center;
      line-height: 50px;
      font-size: 14px;
      border-bottom: 1px solid #cccccc88;
    }
    .active {
      background-color: #e8f4ff;
      color: #1890ff;
    }
  }
}

::v-deep .gever-table-pagination {
  margin-top: 5px !important;
}

</style>
