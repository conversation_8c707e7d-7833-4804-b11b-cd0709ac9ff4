/*
 * @Description: 
 * @Version: 
 * @Author: luozc
 * @Date: 2023-10-12 15:40:32
 * @LastEditors: luozc
 * @LastEditTime: 2023-10-18 19:54:46
 */
import request from '@/utils/request'

/**
 * 获取资产对接前置条件控制标识
 * @param {*} params 
 * @returns 
 */
export function getAssetResourceDockFlag (params) {
    return request({
        url: '/financial/asset/assetResourceDock/getAssetResourceDockFlag',
        method: 'get',
        params
    })
}
/**
 * 获取资产管理列表数据
 * @param {*} data 
 * @returns 
 */
export function getResourceDockList (data) {
    return request({
        url: '/financial/asset/assetResourceDock/list',
        method: 'post',
        data,
        payload: true
    })
}

/**
 * 获取资产管理处置列表数据
 * @param {*} data 
 * @returns 
 */
export function getHandleAssetList (data) {
    return request({
        url: '/financial/asset/assetResourceDock/handleAsset/list',
        method: 'post',
        data,
        payload: true
    })
}

/**
 * 关联
 * @param {*} data 
 * @returns 
 */
export function relate ({ data, params }) {
    return request({
        url: `/financial/asset/assetResourceDock/relate`,
        method: 'post',
        params,
        data,
        payload: true
    })
}

/**
 * 生成卡片
 * @param {*} data 
 * @param {*} booksId
 * @returns 
 */
export function saveCard (data, booksId) {
    return request({
        url: `/financial/asset/assetResourceDock/save?booksId=${booksId}`,
        method: 'post',
        data,
        payload: true
    })
}

/**
 * 撤销生成-关联
 * @param {*} params 
 * @returns 
 */
export function cancel (params) {
    return request({
        url: '/financial/asset/assetResourceDock/cancel',
        method: 'post',
        params
    })
}

/**
 * 资产处置导入初始化
 * @param {*} params 
 * @returns 
 */
export function importInit (params) {
    return request({
        url: '/financial/asset/assetResourceDock/handleAsset/init',
        method: 'get',
        params
    })
}

/**
 * 资产处置导入
 * @param {*} data 
 * @returns 
 */
export function assetImport ({ params, data }) {
    return request({
        url: '/financial/asset/assetResourceDock/handleAsset/import',
        method: 'post',
        params,
        data,
        payload: true
    })
}